package com.nsy.wms.utils;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

public class StockinUtils {


    //判断是否为新款
    public static boolean isNew(List<String> labelAttributeNames, boolean isFirstOrderLabel) {
        if (isFirstOrderLabel)
            return isFirstOrderLabel;
        if (CollectionUtils.isEmpty(labelAttributeNames)) {
            return false;
        }
        //包含新品首单 采购首单 或 新款标签
        return labelAttributeNames.stream().filter(StringUtils::hasText).anyMatch(detail -> detail.contains("新款")
            || detail.contains("新品首单")
            || detail.contains("采购首单"));
    }
}
