package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareStockoutOrderPageRequest;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclareStockoutOrderResponse;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareStockoutOrderEntity;
import org.apache.ibatis.annotations.Param;

@org.apache.ibatis.annotations.Mapper
public interface StockoutCustomsDeclareStockoutOrderMapper extends BaseMapper<StockoutCustomsDeclareStockoutOrderEntity> {
    /**
     * 分页
     *
     * @param request
     * @return
     */
    Page<StockoutCustomsDeclareStockoutOrderResponse> findPage(IPage page, @Param("request") StockoutCustomsDeclareStockoutOrderPageRequest request);
}
