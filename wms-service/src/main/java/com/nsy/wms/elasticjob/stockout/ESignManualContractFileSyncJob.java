package com.nsy.wms.elasticjob.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.wms.domain.esign.ESignSignFlowListRequest;
import com.nsy.api.wms.domain.esign.ESignSignFlowListResponse;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.wms.business.domain.bo.esign.ESignGetSignedFilesResponse;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.esign.ESignService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareContractService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * E签宝合同文件同步定时任务
 * 用于定时获取已签署的E签宝合同，下载并上传到OSS，关联到报关合同
 */
@Component
public class ESignManualContractFileSyncJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(ESignManualContractFileSyncJob.class);

    public static final String JOB_NAME = "ESignContractSyncJob";

    @Autowired
    private ESignService eSignService;
    @Autowired
    private StockoutCustomsDeclareContractService contractService;
    @Autowired
    private BdSystemParameterService bdSystemParameterService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        try {
            LOGGER.info("E签宝合同文件同步任务开始");
            LoginInfoService.setName(JOB_NAME);

            Integer month = bdSystemParameterService.getIntValue(BdSystemParameterEnum.WMS_MANUAL_CONTRACT_FILE_SYNC_MONTH);

            syncESignContracts(month);

            LOGGER.info("E签宝合同文件同步任务结束");
        } finally {
            LoginInfoService.removeName();
        }
    }

    /**
     * 同步E签宝合同文件到OSS
     * 按月份分批查询签署流程，先获取合同文件名，再去找对应的合同
     */
    public void syncESignContracts(Integer monthNum) {
        LOGGER.info("开始同步E签宝合同文件到OSS");

        for (int i = 0; i < monthNum; i++) {
            DateTime tempDateTime = DateUtil.offsetMonth(new Date(), -i);
            syncContractSignInfo(DateUtil.beginOfMonth(tempDateTime), DateUtil.endOfMonth(tempDateTime));
        }
    }

    /**
     * 同步指定月份的合同
     */
    private void syncContractSignInfo(DateTime begin, DateTime end) {
        LOGGER.info("开始处理 {} - {} 的签署流程", DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end));

        int pageNum = 1;
        int pageSize = 50;
        while (true) {
            // 获取该月份的签署流程
            List<ESignSignFlowListResponse.SignFlowInfo> flows = getCompletedSignFlowsForMonth(
                    begin.getTime(), end.getTime(), pageNum, pageSize);

            if (CollectionUtil.isEmpty(flows)) {
                break;
            }

            LOGGER.info("{}   第 {} 页获取到 {} 个签署流程",
                    DateUtil.formatDate(begin), pageNum, flows.size());

            // 处理每个签署流程
            for (ESignSignFlowListResponse.SignFlowInfo flow : flows) {
                // 1. 先获取该流程中的所有已签署文件
                ESignGetSignedFilesResponse signedFiles = eSignService.getSignedFiles(flow.getSignFlowId());
                // 2. 遍历每个文件，通过文件名去找对应的合同
                for (ESignGetSignedFilesResponse.File file : signedFiles.getFiles()) {
                    // 通过文件名匹配合同
                    String declareContractNo = StrUtil.subBefore(file.getFileName(), '.', true);
                    StockoutCustomsDeclareContractEntity matchedContract = contractService.getManualContract(declareContractNo);
                    if (Objects.isNull(matchedContract)) {
                        LOGGER.info("没找到合同 {} ", declareContractNo);
                        continue;
                    }
                    contractService.updateManualSignInfo(matchedContract.getDeclareContractId(), flow.getSignFlowId(), file.getFileId());
                }
            }

            // 如果返回的数据少于pageSize，说明没有更多数据了
            if (flows.size() < pageSize) {
                break;
            } else {
                pageNum++;
            }
        }
    }

    /**
     * 获取指定月份的已完成签署流程
     */
    private List<ESignSignFlowListResponse.SignFlowInfo> getCompletedSignFlowsForMonth(
            long startTime, long endTime, int pageNum, int pageSize) {
        ESignSignFlowListRequest request = new ESignSignFlowListRequest();
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        request.setSignFlowStartTimeFrom(startTime);
        request.setSignFlowStartTimeTo(endTime);
        request.setSignFlowStatus(Collections.singletonList(2)); // 2表示已完成状态

        ESignSignFlowListResponse response = eSignService.getSignFlowList(request);
        return response.getSignFlowInfos();
    }
} 