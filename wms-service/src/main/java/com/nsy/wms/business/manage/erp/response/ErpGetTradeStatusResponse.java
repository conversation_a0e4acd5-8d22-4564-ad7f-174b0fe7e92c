package com.nsy.wms.business.manage.erp.response;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 获取订单状态响应
 */
public class ErpGetTradeStatusResponse {

    @JsonProperty("Tid")
    private String tid;

    @JsonProperty("OrderState")
    private Integer orderState;

    @JsonProperty("IsPay")
    private Integer isPay;

    @JsonProperty("IsHangUp")
    private Integer isHangUp;

    @JsonProperty("OrderDeleteState")
    private Integer orderDeleteState;

    @JsonProperty("PaymentStatus")
    private String paymentStatus;

    @JsonProperty("OrderStateName")
    private String orderStateName;

    //是否允许发货 1 允许 ，0不允许
    @JsonProperty("IsDeliveryAllowed")
    private Integer isDeliveryAllowed;

    @JsonProperty("DeliveryMessage")
    private String deliveryMessage;

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Integer getOrderState() {
        return orderState;
    }

    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }

    public Integer getIsPay() {
        return isPay;
    }

    public void setIsPay(Integer isPay) {
        this.isPay = isPay;
    }

    public Integer getIsHangUp() {
        return isHangUp;
    }

    public void setIsHangUp(Integer isHangUp) {
        this.isHangUp = isHangUp;
    }

    public Integer getOrderDeleteState() {
        return orderDeleteState;
    }

    public void setOrderDeleteState(Integer orderDeleteState) {
        this.orderDeleteState = orderDeleteState;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getOrderStateName() {
        return orderStateName;
    }

    public void setOrderStateName(String orderStateName) {
        this.orderStateName = orderStateName;
    }

    public Integer getIsDeliveryAllowed() {
        return isDeliveryAllowed;
    }

    public void setIsDeliveryAllowed(Integer isDeliveryAllowed) {
        this.isDeliveryAllowed = isDeliveryAllowed;
    }

    public String getDeliveryMessage() {
        return deliveryMessage;
    }

    public void setDeliveryMessage(String deliveryMessage) {
        this.deliveryMessage = deliveryMessage;
    }
}
