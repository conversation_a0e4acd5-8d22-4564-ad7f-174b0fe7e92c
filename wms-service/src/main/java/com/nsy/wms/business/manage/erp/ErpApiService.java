package com.nsy.wms.business.manage.erp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.external.ExternalOrderCheckQueueStatusEnum;
import com.nsy.api.wms.enumeration.stock.SyncErpTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.request.stockout.SyncReceiverInfoListRequest;
import com.nsy.api.wms.request.stockout.SyncReceiverInfoRequest;
import com.nsy.api.wms.response.stock.ErpSpotGenerateDocumentsResponse;
import com.nsy.api.wms.response.stock.ErpSpotInfoResponse;
import com.nsy.wms.business.domain.bo.mq.SyncErpMessage;
import com.nsy.wms.business.manage.erp.request.ErpBuyerInfoBySupplierIdRequest;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackReceivedNewRequest;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackReceivedRequest;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackShelvedBoxInfo;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackShelvedInfo;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackShelvedRequest;
import com.nsy.wms.business.manage.erp.request.ErpGeneratePurchaseReceivingOrderRequest;
import com.nsy.wms.business.manage.erp.request.ErpGetAmazonTradeInfoRequest;
import com.nsy.wms.business.manage.erp.request.ErpGetSupplierInfoRequest;
import com.nsy.wms.business.manage.erp.request.ErpGetTradeStatusRequest;
import com.nsy.wms.business.manage.erp.request.ErpInventoryRequest;
import com.nsy.wms.business.manage.erp.request.ErpProcessTransferOutRequest;
import com.nsy.wms.business.manage.erp.request.ErpReturnProductItemSyncRequest;
import com.nsy.wms.business.manage.erp.request.ErpReturnProductRequest;
import com.nsy.wms.business.manage.erp.request.ErpReturnProductSyncRequest;
import com.nsy.wms.business.manage.erp.request.ErpSpotGenerateDocumentsItemRequest;
import com.nsy.wms.business.manage.erp.request.ErpSpotGenerateDocumentsRequest;
import com.nsy.wms.business.manage.erp.request.ErpStockLendCantReturnRequest;
import com.nsy.wms.business.manage.erp.request.ErpStockLendReturnRequest;
import com.nsy.wms.business.manage.erp.request.ErpSyncLackRequest;
import com.nsy.wms.business.manage.erp.request.ErpSyncPrintExceptionRequest;
import com.nsy.wms.business.manage.erp.request.ErpSyncStockRequest;
import com.nsy.wms.business.manage.erp.request.ErpSyncStockoutOrderLogRequest;
import com.nsy.wms.business.manage.erp.request.ErpUpdateLogisticsRequest;
import com.nsy.wms.business.manage.erp.request.ErpUpdateStockSpaceIsLock;
import com.nsy.wms.business.manage.erp.request.FeedbackConcessionReceiveRequest;
import com.nsy.wms.business.manage.erp.request.FeedbackPackageQtyRequest;
import com.nsy.wms.business.manage.erp.request.SyncPlatformScheduleToErpRequest;
import com.nsy.wms.business.manage.erp.request.check.ErpOrderCheckStockinRequest;
import com.nsy.wms.business.manage.erp.request.check.ErpOrderCheckStockoutRequest;
import com.nsy.wms.business.manage.erp.response.ErpBuyerInfoBySupplierIdResponse;
import com.nsy.wms.business.manage.erp.response.ErpCheckMsg;
import com.nsy.wms.business.manage.erp.response.ErpGetAmazonTradeInfoResponse;
import com.nsy.wms.business.manage.erp.response.ErpGetTradeStatusResponse;
import com.nsy.wms.business.manage.erp.response.ErpReturnApplyInfoResponse;
import com.nsy.wms.business.manage.erp.response.ErpReturnProductInfoResponse;
import com.nsy.wms.business.manage.erp.response.ErpSupplierEmpNameInfoResponse;
import com.nsy.wms.business.manage.erp.response.ErpSupplierInfoResponse;
import com.nsy.wms.business.manage.erp.response.ErpSyncStockResponse;
import com.nsy.wms.business.manage.erp.response.StoreData;
import com.nsy.wms.business.manage.erp.response.StoreListResponse;
import com.nsy.wms.business.manage.erp.response.check.ErpOrderCheckStockinResponse;
import com.nsy.wms.business.manage.erp.response.check.ErpOrderCheckStockoutResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalApiAsyncQueueService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.external.ExternalOrderCheckResultService;
import com.nsy.wms.business.service.internal.common.PurchaseModuleService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.external.ExternalOrderCheckQueueEntity;
import com.nsy.wms.repository.entity.external.ExternalOrderCheckResultEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ErpApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ErpApiService.class);
    @Inject
    ErpApiRestClient erpApiRestClient;
    @Value("${nsy.service.url.erp}")
    private String erpApiServiceUrl;
    @Autowired
    private ExternalApiLogService externalApiLogService;
    @Autowired
    private ExternalOrderCheckResultService checkResultService;
    @Autowired
    ExternalApiAsyncQueueService externalApiAsyncQueueService;
    @Autowired
    PurchaseModuleService purchaseModuleService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    ErpStockinApiService erpStockinApiService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    private ObjectMapper objectMapper;


    /**
     * 获取用户（权限过滤） 的店铺数据
     *
     * <AUTHOR>
     * 2021-08-18
     */
    @Cacheable(value = CacheKeyConstant.WMS_USER_STORE_LIST, key = "#userName")
    public List<StoreData> getPermissionStoreList(String userName) {
        try {
            StoreListResponse response = erpApiRestClient.buildApi(erpApiServiceUrl, "/User/GetPermissionStoreList?userName=" + userName).get(StoreListResponse.class);
            return response.getList();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取供应商信息
     *
     * @return
     */
    @Cacheable(value = CacheKeyConstant.SUPPLIER_INFO_SELECT, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public ErpSupplierInfoResponse getSupplierInfo() {
        ErpSupplierInfoResponse response = new ErpSupplierInfoResponse();
        try {
            response = erpApiRestClient.buildApi(erpApiServiceUrl, "/SupplierPurchase/GetSupplierNameInfo").get(ErpSupplierInfoResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return response;
    }

    /**
     * 获取供应商信息
     *
     * @return
     */
    public ErpSupplierInfoResponse getSupplierInfo(ErpGetSupplierInfoRequest request) {
        ErpSupplierInfoResponse response = new ErpSupplierInfoResponse();
        try {
            response = erpApiRestClient.buildApi(erpApiServiceUrl, "/SupplierPurchase/GetSupplierNameInfoByProductIds").post(request, ErpSupplierInfoResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return response;
    }

    /**
     * 获取供应商采购员信息
     *
     * @return
     */
    @Cacheable(value = CacheKeyConstant.SUPPLIER_EMP_INFO_SELECT, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public ErpSupplierEmpNameInfoResponse getSupplierEmpNameInfo() {
        ErpSupplierEmpNameInfoResponse response = new ErpSupplierEmpNameInfoResponse();
        try {
            response = erpApiRestClient.buildApi(erpApiServiceUrl, "/SupplierPurchase/GetSupplierEmpNameInfo").get(ErpSupplierEmpNameInfoResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return response;
    }

    /**
     * 退货登记完成扫描
     *
     * @return
     */
    public ErpReturnApplyInfoResponse completeReturnProduct(ErpReturnProductRequest request) {
        ErpReturnApplyInfoResponse applyInfoResponse;
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.COMPLETE_RETURN_PRODUCT, String.format("%s/Product/CompleteReturnProduct", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getReturnBookInInfo().getLogisticsNo(), String.format("wms物流单号【%s】完成退货登记扫描", request.getReturnBookInInfo().getLogisticsNo()));
        try {
            applyInfoResponse = erpApiRestClient.buildApi(erpApiServiceUrl, "/Product/CompleteReturnProduct").post(request, ErpReturnApplyInfoResponse.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(applyInfoResponse), ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
        return applyInfoResponse;
    }

    /**
     * 退货登记完成扫描
     *
     * @return
     */
    public ErpCheckMsg checkReturnProduct(ErpReturnProductRequest request) {
        ErpCheckMsg erpCheckMsg = new ErpCheckMsg();
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.CHECK_RETURN_PRODUCT, String.format("%s/Product/CheckReturnProduct", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getReturnBookInInfo().getLogisticsNo(), String.format("wms物流单号【%s】校验退货登记扫描", request.getReturnBookInInfo().getLogisticsNo()));
        try {
            erpCheckMsg = erpApiRestClient.buildApi(erpApiServiceUrl, "/Product/CheckReturnProduct").post(request, ErpCheckMsg.class);
            if (erpCheckMsg.getIsError()) {
                externalApiLogService.updateLog(apiLogEntity, erpCheckMsg.getMessage(), ExternalApiLogStatusEnum.FAIL);
            } else {
                externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(erpCheckMsg), ExternalApiLogStatusEnum.SUCCESS);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
        }
        return erpCheckMsg;
    }

    /**
     * 出库单日志同步
     *
     * @param request
     */
    public void syncStockoutOrderLog(ErpSyncStockoutOrderLogRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_STOCKOUT_ORDER_LOG, String.format("%s/NewWms/SyncStockoutOrderLog", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getOrderNo(), "订单日志同步ERP");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWms/SyncStockoutOrderLog").post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    // 生成盘点单
    public void inventory(ErpInventoryRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GENERATE_INVENTORY, String.format("%s/NewWms/Inventory", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getInventoryItemList().get(0).getSku(), String.format("wms盘点库位【%s】,同步OMS生成盘点单", request.getPositionCode()));
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWms/Inventory").post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 内部借用归还
     *
     * @param request
     */
    public void asyncStockLendReturn(ErpStockLendReturnRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.STOCK_LEND_RETURN, String.format("%s/NewWms/StockLendReturn", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getStockLendCode(), String.format("wms借用单【%s】归还,同步OMS", request.getStockLendCode()));

        //保存到异步调用队列
        externalApiAsyncQueueService.add(apiLogEntity, request.getStockLendCode());
    }

    /**
     * 内部借用待归还 (异步)
     *
     * @param request
     */
    public void asyncStockLendWaitReturn(ErpStockLendReturnRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.STOCK_LEND_WAIT_RETURN, String.format("%s/NewWms/StockLendWaitReturn", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getStockLendCode(), String.format("wms借用单【%s】借出,同步OMS", request.getStockLendCode()));

        //保存到异步调用队列
        externalApiAsyncQueueService.add(apiLogEntity, request.getStockLendCode());
    }

    /**
     * 库存借用无法归还
     *
     * @param request
     */
    public void asyncStockLendCantReturn(ErpStockLendCantReturnRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.STOCK_LEND_CANT_RETURN, String.format("%s/NewWms/StockLendCantReturn", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getStockLendCode(), String.format("wms借用单【%s】无法归还,同步OMS", request.getStockLendCode()));

        //保存到异步调用队列
        externalApiAsyncQueueService.add(apiLogEntity, request.getStockLendCode());
    }

    /**
     * 库存借用取消
     *
     * @param request
     */
    public void asyncStockLendCancel(ErpStockLendCantReturnRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.STOCK_LEND_CANCEL, String.format("%s/NewWms/StockLendCancel", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getStockLendCode(), String.format("wms取消借用单【%s】,同步OMS", request.getStockLendCode()));

        //保存到异步调用队列
        externalApiAsyncQueueService.add(apiLogEntity, request.getStockLendCode());
    }

    // 采购退货，反馈退货数量, 同步接收单退货数
    public ErpCheckMsg returnProductSync(ErpReturnProductSyncRequest request) {
        ErpCheckMsg checkMsg = new ErpCheckMsg();
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.RETURN_PRODUCT_SYNC, String.format("%s/SupplierPurchase/ReturnProductSync", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getItems().stream().map(ErpReturnProductItemSyncRequest::getSku).distinct().collect(Collectors.joining(StringConstant.COMMA)), "反馈退货数量,增加退货仓库存");
        try {
            checkMsg = erpApiRestClient.buildApi(erpApiServiceUrl, "/SupplierPurchase/ReturnProductSync").post(request, ErpCheckMsg.class);
            if (checkMsg.getIsError()) {
                externalApiLogService.updateLog(apiLogEntity, checkMsg.getMessage(), ExternalApiLogStatusEnum.FAIL);
            } else {
                externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(checkMsg), ExternalApiLogStatusEnum.SUCCESS);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
        }
        return checkMsg;
    }

    // 采购退货，异步反馈退货数量
    public void returnProductAsync(ErpReturnProductSyncRequest request, String supplierDeliveryBoxCode) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLogRequired(ExternalApiInfoEnum.RETURN_PRODUCT_SYNC, String.format("%s/SupplierPurchase/ReturnProductSync", erpApiServiceUrl),
                JsonMapper.toJson(request), supplierDeliveryBoxCode, String.format("反馈退货数量,增加退货仓库存，sku: %s", request.getItems().stream().map(ErpReturnProductItemSyncRequest::getSku).distinct().collect(Collectors.joining(StringConstant.COMMA))));
        externalApiAsyncQueueService.addEntityToKafka(apiLogEntity, supplierDeliveryBoxCode);
        //发送消息给kafka
        String sku = request.getItems().get(0).getSku();
        SyncErpMessage<Integer> syncErpMessage = new SyncErpMessage<>(TenantContext.getTenant(), loginInfoService.getName(), supplierDeliveryBoxCode, SyncErpTypeEnum.SHELVE, apiLogEntity.getApiLogId());
        syncErpMessage.setApiName(apiLogEntity.getApiName());
        messageProducer.sendMessage(KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC_NAME, KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC, Key.of(TenantContext.getTenant() + "_" + sku), syncErpMessage);

    }

    // 同步存储、零捡库存
    public ErpSyncStockResponse updateStockSync(ErpSyncStockRequest request) {
        ErpSyncStockResponse result = new ErpSyncStockResponse();
        try {
            return erpApiRestClient.buildApi(erpApiServiceUrl, "/Stock/WmsUpdateStock").post(request, ErpSyncStockResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 生成接收单(异步)
     *
     * @param request
     */
    public void asyncGeneratePurchaseReceivingOrder(ErpGeneratePurchaseReceivingOrderRequest request, boolean isDelay) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GENERATE_RECEIVING_ORDER, String.format("%s/NewWms/GeneratePurchaseReceivingOrder", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getSupplierDeliveryBoxCode(), String.format("wms出库箱码【%s】反馈上架接收", request.getSupplierDeliveryBoxCode()));

        if (isDelay) {
            //保存到异步调用队列
            externalApiAsyncQueueService.add(apiLogEntity, request.getSupplierDeliveryBoxCode());
        } else {
            externalApiAsyncQueueService.addEntityToKafka(apiLogEntity, request.getSupplierDeliveryBoxCode());
            //发送消息给kafka
            SyncErpMessage<Integer> syncErpMessage = new SyncErpMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request.getSupplierDeliveryBoxCode(), SyncErpTypeEnum.SHELVE, apiLogEntity.getApiLogId());
            syncErpMessage.setApiName(apiLogEntity.getApiName());
            messageProducer.sendMessage(KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC_NAME, KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC, Key.of(request.getSupplierDeliveryBoxCode()), syncErpMessage);
        }


    }

    /**
     * 反馈上架接收=》同步接收数，入库数
     *
     * @param request
     */
    public void feedbackReceived(ErpFeedbackReceivedRequest request, String positionCode, String stockinType) {
        //2022-04-18 修改为异步调用
        //2022-06-10 .net增加事务，要求一个sku发送一次请求

        //其他类型的入库单调用另外一个接口
        if (StockinTypeEnum.OVER.name().equals(stockinType)) {
            erpStockinApiService.syncReceivingByOtherStockIn(request);
            return;
        }
        List<ErpFeedbackReceivedRequest.ErpFeedbackReceivedItem> feedbackReceivedItemList = request.getFeedbackReceivedItemList();

        feedbackReceivedItemList.forEach(item -> {
            ErpFeedbackReceivedNewRequest erpFeedbackReceivedNewRequest = new ErpFeedbackReceivedNewRequest();
            BeanUtilsEx.copyProperties(request, erpFeedbackReceivedNewRequest);
            BeanUtilsEx.copyProperties(item, erpFeedbackReceivedNewRequest);
            erpFeedbackReceivedNewRequest.setShelfTime(new Date());
            erpFeedbackReceivedNewRequest.setIsNegativeUpShelf(0);

            ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLogRequired(ExternalApiInfoEnum.FEEDBACK_RECEIVED, String.format("%s/NewWms/newFeedbackReceived", erpApiServiceUrl),
                    JsonMapper.toJson(erpFeedbackReceivedNewRequest), request.getSupplierDeliveryBoxCode(), String.format(erpFeedbackReceivedNewRequest.getShelvedQty() > 0 ? "wms出库箱码【%s】【%s】反馈上架接收，反馈接收数" : "wms出库箱码【%s】编辑【%s】上架数量，反馈接收数", erpFeedbackReceivedNewRequest.getSupplierDeliveryBoxCode(), erpFeedbackReceivedNewRequest.getSku()));
            //保存到异步调用队列
            externalApiAsyncQueueService.addEntityToKafka(apiLogEntity, erpFeedbackReceivedNewRequest.getSupplierDeliveryBoxCode());
            //发送消息给kafka
            SyncErpMessage<Integer> syncErpMessage = new SyncErpMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request.getSupplierDeliveryBoxCode(), SyncErpTypeEnum.SHELVE, apiLogEntity.getApiLogId());
            syncErpMessage.setApiName(apiLogEntity.getApiName());
            messageProducer.sendMessage(KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC_NAME, KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC, Key.of(TenantContext.getTenant() + "_" + item.getSku()), syncErpMessage);

            //反馈api-common
            purchaseModuleService.pushStockinToEtl(null, item.getStockinOrderItemId(), positionCode);
        });
    }

    // 反馈上架接收=》  新接口
    public void newFeedbackReceived(ErpFeedbackReceivedNewRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLogRequired(ExternalApiInfoEnum.FEEDBACK_EDIT_RECEIVED, String.format("%s/NewWms/newFeedbackReceived", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getSupplierDeliveryBoxCode(), String.format(request.getShelvedQty() > 0 ? "wms出库箱码【%s】【%s】反馈上架接收，反馈接收数" : "wms出库箱码【%s】编辑【%s】上架数量，反馈接收数", request.getSupplierDeliveryBoxCode(), request.getSku()));
        //保存到异步调用队列
        externalApiAsyncQueueService.addEntityToKafka(apiLogEntity, request.getSupplierDeliveryBoxCode());
        //发送消息给kafka
        SyncErpMessage<Integer> syncErpMessage = new SyncErpMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request.getSupplierDeliveryBoxCode(), SyncErpTypeEnum.SHELVE, apiLogEntity.getApiLogId());
        syncErpMessage.setApiName(apiLogEntity.getApiName());
        messageProducer.sendMessage(KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC_NAME, KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC, Key.of(TenantContext.getTenant() + "_" + request.getSku()), syncErpMessage);

        /*
messageProducer.sendMessage(KafkaConstant.EXTERNAL_API_ASYNC_TOPIC_NAME, KafkaConstant.EXTERNAL_API_ASYNC_TOPIC,
                new LocationWrapperMessage<>(TenantContext.getTenant(), apiLogEntity.getCreateBy(),
                        request.getSupplierDeliveryBoxCode() + "-" + request.getSku(), apiLogEntity.getApiLogId()));*/
    }


    // 让步接收反馈
    public void feedbackConcessionReceive(FeedbackConcessionReceiveRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.FEEDBACK_CONCESSION_RECEIVED, String.format("%s/NewWms/FeedbackConcessionReceive", erpApiServiceUrl),
                JsonMapper.toJson(request), String.join(StringConstant.COMMA, request.getSupplierDeliveryBoxCode()), "让步接收反馈");
        //保存到异步调用队列
        externalApiAsyncQueueService.addIgnore(apiLogEntity, request.getSupplierDeliveryBoxCode());
    }

    // 包装数量反馈反馈
    public void feedbackPackageQty(FeedbackPackageQtyRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.FEEDBACK_PACKAGE_QTY, String.format("%s/NewWms/FeedbackPackageQty", erpApiServiceUrl),
                JsonMapper.toJson(request), String.join(StringConstant.COMMA, request.getSupplierDeliveryBoxCode()), "包装数反馈");
        //保存到异步调用队列
        externalApiAsyncQueueService.addIgnore(apiLogEntity, request.getSupplierDeliveryBoxCode());
    }


    /**
     * 反馈上架完成=》更改出库单状态
     *
     * @param request
     */
    public void feedbackShelvedAsync(ErpFeedbackShelvedRequest request) {
        for (ErpFeedbackShelvedInfo requestItem : request.getSupplierDeliveryNoList()) {
            for (ErpFeedbackShelvedBoxInfo deliveryBox : requestItem.getSupplierDeliveryBoxList()) {
                if (deliveryBox.getIsChecked().equals(0))
                    continue;
                //一个出库箱码只调用一次
                if (checkHasFeedbackShelvedToErp(deliveryBox.getSupplierDeliveryBoxNo()))
                    continue;
                ErpFeedbackShelvedRequest erpRequest = new ErpFeedbackShelvedRequest();
                ErpFeedbackShelvedInfo info = new ErpFeedbackShelvedInfo();
                List<ErpFeedbackShelvedBoxInfo> erpFeedbackShelvedBoxInfos = Collections.singletonList(deliveryBox);
                info.setSupplierDeliveryBoxList(erpFeedbackShelvedBoxInfos);
                info.setSupplierDeliveryNo(requestItem.getSupplierDeliveryNo());
                List<ErpFeedbackShelvedInfo> erpFeedbackShelvedInfos = Collections.singletonList(info);
                erpRequest.setLocation(request.getLocation());
                erpRequest.setOperator(request.getOperator());
                erpRequest.setShelfTime(new Date());
                erpRequest.setSupplierDeliveryNoList(erpFeedbackShelvedInfos);
                ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.FEEDBACK_SHELVED, String.format("%s/NewWms/FeedbackShelved", erpApiServiceUrl),
                        JsonMapper.toJson(erpRequest), deliveryBox.getSupplierDeliveryBoxNo(), "wms上架完成反馈erp");
                //保存到异步调用队列
                externalApiAsyncQueueService.addIgnore(apiLogEntity, deliveryBox.getSupplierDeliveryBoxNo());
            }
        }
    }

    /**
     * 一个出库箱码只调用一次
     *
     * @param supplierDeliveryBoxCode
     * @return
     */
    public Boolean checkHasFeedbackShelvedToErp(String supplierDeliveryBoxCode) {
        ExternalApiLogEntity externalApiLogEntity = externalApiLogService.list(new LambdaQueryWrapper<ExternalApiLogEntity>()
                        .eq(ExternalApiLogEntity::getDocumentNo, supplierDeliveryBoxCode)
                        .eq(ExternalApiLogEntity::getApiName, ExternalApiInfoEnum.FEEDBACK_SHELVED.getApiName())
                        .eq(ExternalApiLogEntity::getStatus, ExternalApiLogStatusEnum.SUCCESS.name()))
                .stream().findFirst().orElse(null);
        if (Objects.isNull(externalApiLogEntity)) {
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }
    }

    /**
     * 扫描单号获取.net装箱数据
     *
     * @param scanNumber
     */
    public ErpReturnProductInfoResponse getReturnProductList(String scanNumber) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.RETURN_PRODUCT_LIST, String.format("%s/NewWms/getReturnProductList", erpApiServiceUrl),
                JsonMapper.toJson(scanNumber), scanNumber, String.format("扫描单号【%s】获取退货数据", scanNumber));
        try {
            ErpReturnProductInfoResponse response = erpApiRestClient.buildApi(erpApiServiceUrl, String.format("/NewWms/getReturnProductList?scanNumber=%s", scanNumber)).get(ErpReturnProductInfoResponse.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(response), ExternalApiLogStatusEnum.SUCCESS);
            return response;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 核对入库模块单据
     */
    public void checkStockin(ErpOrderCheckStockinRequest request, ExternalOrderCheckQueueEntity queueEntity) {
        ExternalOrderCheckResultEntity resultEntity = checkResultService.createBaseEntity(queueEntity, request.getOperator(), String.format("%s/NewWms/CheckStockin", erpApiServiceUrl), JsonMapper.toJson(request));
        try {
            ErpOrderCheckStockinResponse response = erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWms/CheckStockin").post(request, ErpOrderCheckStockinResponse.class);
            if (response.getIsError())
                queueEntity.setStatus(ExternalOrderCheckQueueStatusEnum.FAIL.name());
            else
                queueEntity.setStatus(ExternalOrderCheckQueueStatusEnum.SUCCESS.name());

            resultEntity.setResponseContent(JsonMapper.toJson(response));
            resultEntity.setUpdateBy(request.getOperator());
            checkResultService.updateById(resultEntity);

        } catch (Exception e) {
            resultEntity.setResponseContent(e.getMessage());
            resultEntity.setUpdateBy(request.getOperator());
            checkResultService.updateById(resultEntity);
            queueEntity.setStatus(ExternalOrderCheckQueueStatusEnum.ERROR.name());
        }
    }

    /**
     * 核对出库模块单据
     */
    public void checkStockout(ErpOrderCheckStockoutRequest request, ExternalOrderCheckQueueEntity queueEntity) {
        ExternalOrderCheckResultEntity resultEntity = checkResultService.createBaseEntity(queueEntity, request.getOperator(), String.format("%s/NewWms/CheckStockout", erpApiServiceUrl), JsonMapper.toJson(request));
        try {
            ErpOrderCheckStockoutResponse response = erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWms/CheckStockout").post(request, ErpOrderCheckStockoutResponse.class);
            if (response.getIsError())
                queueEntity.setStatus(ExternalOrderCheckQueueStatusEnum.FAIL.name());
            else
                queueEntity.setStatus(ExternalOrderCheckQueueStatusEnum.SUCCESS.name());

            resultEntity.setResponseContent(JsonMapper.toJson(response));
            resultEntity.setUpdateBy(request.getOperator());
            checkResultService.updateById(resultEntity);

        } catch (Exception e) {
            resultEntity.setResponseContent(e.getMessage());
            resultEntity.setUpdateBy(request.getOperator());
            checkResultService.updateById(resultEntity);
            queueEntity.setStatus(ExternalOrderCheckQueueStatusEnum.ERROR.name());
        }
    }

    /**
     * 面单打印异常同步erp
     *
     * @param request
     */
    public void syncPrintException(ErpSyncPrintExceptionRequest request) {
        if (StringUtils.hasText(request.getContent()) && request.getContent().length() > 500) {
            request.setContent(request.getContent().substring(0, 500));
        }
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_PRINT_EXCEPTION, String.format("%s/NewWms/SyncPrintException", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getTid(), "面单打印异常同步erp");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWms/SyncPrintException").post(request, ErpSpotInfoResponse.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 次品调拨出库
     */
    public void addTransferOutByLossQty(ErpProcessTransferOutRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.ADD_PROCESS_TRANSFER_OUT, String.format("%s/NewWmsProcess/AddTransferOutByLossQty", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getOldSku(), "次品调拨出库");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWmsProcess/AddTransferOutByLossQty").post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 出库单修改物流未同步拣货单
     */
    public void updateLogistics(ErpUpdateLogisticsRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.UPDATE_LOGISTICS, String.format("%s/NewWms/UpdateLogistics", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getErpPickIds(), "出库单修改物流未同步拣货单");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWms/UpdateLogistics").post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    // 确定缺货同步
    public void syncLack(Integer erpPickId, String userName) {
        ErpSyncLackRequest request = new ErpSyncLackRequest();
        request.setErpPickId(erpPickId);
        request.setUserName(userName);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_LACK, String.format("%s/NewWms/SyncLack", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getErpPickId().toString(), "出库单确认缺货同步erp");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWms/SyncLack").post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    // 月台同步erp
    public void syncPlatformScheduleToErp(SyncPlatformScheduleToErpRequest request) {
        String url = "/NewWms/SyncAddSupplierDelivery";
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_PLATFORM_SCHEDULE, String.format("%s%s", erpApiServiceUrl, url),
                JsonMapper.toJson(request), request.getSupplierDeliveryNo(), "月台同步erp");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, url).post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    // 退货上架 补出库单、出库单明细、接收单主表
    public void returnShelveAddDocuments(SyncPlatformScheduleToErpRequest request) {
        String url = "/NewWms/ReturnShelveAddDocuments";
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.RETURN_SHELVE, String.format("%s%s", erpApiServiceUrl, url),
                JsonMapper.toJson(request), request.getSupplierDeliveryNo(), "退货上架,补erp单据");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, url).post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    // 根据供应商id获取供应商采购员的信息
    public ErpBuyerInfoBySupplierIdResponse getBuyerInfoBySupplierIdList(ErpBuyerInfoBySupplierIdRequest request) {
        String url = "/SupplierPurchase/GetBuyerInfoBySupplierIdList";
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GET_SUPPLIER_INFO, String.format("%s%s", erpApiServiceUrl, url),
                JsonMapper.toJson(request), request.getSupplierIdList().toString(), "获取供应商采购员的信息");
        ErpBuyerInfoBySupplierIdResponse response = new ErpBuyerInfoBySupplierIdResponse();
        try {
            response = erpApiRestClient.buildApi(erpApiServiceUrl, url).post(request, ErpBuyerInfoBySupplierIdResponse.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(response), ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
        }
        return response;
    }

    // 获取亚马逊订单信息 refrenceid + 仓库名
    @Deprecated
    public ErpGetAmazonTradeInfoResponse getAmazonTradeInfo(ErpGetAmazonTradeInfoRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GETAMAZONTRADEINFO, String.format("%s/NewWms/GetAmazonTradeInfo", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getOrderNos().get(0), "获取亚马逊订单信息");
        try {
            ErpGetAmazonTradeInfoResponse response = erpApiRestClient.buildApi(erpApiServiceUrl, "/NewWms/GetAmazonTradeInfo").post(request, ErpGetAmazonTradeInfoResponse.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(response), ExternalApiLogStatusEnum.SUCCESS);
            return response;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    // 现货收货生成工厂出库单主表,接收单主表
    public ErpSpotGenerateDocumentsResponse spotGenerateDocuments(ErpSpotGenerateDocumentsRequest request) {
        String url = "/NewWms/SpotGenerateDocuments";
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SPOT_GENERATE_DOCUMENTS, String.format("%s%s", erpApiServiceUrl, url),
                JsonMapper.toJson(request), request.getStockinOrderNo(), "现货收货");
        try {
            ErpSpotGenerateDocumentsResponse response = erpApiRestClient.buildApi(erpApiServiceUrl, url).post(request, ErpSpotGenerateDocumentsResponse.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(response), ExternalApiLogStatusEnum.SUCCESS);
            return response;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    // wms现货收货绑定内部箱，生成出库单明细、有退货生成接收单明细
    public void spotGenerateDocumentsItem(ErpSpotGenerateDocumentsItemRequest request) {
        String url = "/NewWms/SpotGenerateDocumentsItem";
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SPOT_GENERATE_DOCUMENTS_ITEM, String.format("%s%s", erpApiServiceUrl, url),
                JsonMapper.toJson(request), request.getSupplierDeliveryNo(), "现货收货绑定内部箱");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, url).post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    // 修改收货地址信息
    public void updateReceiverInfo(SyncReceiverInfoListRequest request) {
        String url = "/NewWms/SyncReceiverInfoTemu";
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.UPDATE_RECEIVER_INFO, String.format("%s%s", erpApiServiceUrl, url),
                JsonMapper.toJson(request), request.getInfoList().stream().map(SyncReceiverInfoRequest::getOrderNo).distinct().collect(Collectors.joining(StringConstant.COMMA)), "修改收货地址");
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, url).post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }


    // 修改收货地址信息
    public void updateStockSpaceIsLock(ErpUpdateStockSpaceIsLock request) {
        String url = "/NewWms/UpdateStockSpaceIsLock";
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.UPDATE_STOCK_IS_LOCK, String.format("%s%s", erpApiServiceUrl, url),
                JsonMapper.toJson(request), request.getSpaceStockItemList().get(0).getSku(), String.format("%s sku库存",
                        request.getSpaceStockItemList().get(0).getIsLock().equals(1) ? "锁定" : "解锁"));
        try {
            erpApiRestClient.buildApi(erpApiServiceUrl, url).post(request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 获取订单状态
     *
     * @param request 包含订单号列表的请求
     * @return 订单状态信息
     */
    public List<ErpGetTradeStatusResponse> getTradeStatus(ErpGetTradeStatusRequest request) {
        String url = "/Trade/GetTradeStatus";
        try {
            String bodyJson = erpApiRestClient.buildApi(erpApiServiceUrl, url).post(request, String.class);
            return objectMapper.readValue(bodyJson, new ErpTradeInfoResponseReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    private static final class ErpTradeInfoResponseReference extends TypeReference<ArrayList<ErpGetTradeStatusResponse>> {
    }

}
