package com.nsy.wms.business.service.product;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.etl.enums.ServiceNameEnum;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.ProductInfoConstant;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.domain.product.ProductFabricTypeInfo;
import com.nsy.api.wms.domain.product.ProductInfo;
import com.nsy.api.wms.domain.product.ProductLocationInfo;
import com.nsy.api.wms.domain.product.ProductSku;
import com.nsy.api.wms.domain.product.ProductSkuInfoMap;
import com.nsy.api.wms.domain.product.ProductSpec;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.product.ProductSpecItem;
import com.nsy.api.wms.domain.product.ProductSpecPrint;
import com.nsy.api.wms.domain.shared.SelectTreeModel;
import com.nsy.api.wms.domain.stockin.SkuPrint;
import com.nsy.api.wms.domain.stockin.StockinReturnProductSkuPrintDTO;
import com.nsy.api.wms.domain.stockin.StockinSpotSkuPrint;
import com.nsy.api.wms.enumeration.OperationEventEnum;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.request.product.ProductSkuRequest;
import com.nsy.api.wms.request.product.ProductSpecPackageBatchRequest;
import com.nsy.api.wms.request.product.SkuInfoListRequest;
import com.nsy.api.wms.request.product.SkuPrintSpotNewRequest;
import com.nsy.api.wms.request.product.SkuPrintSpotRequest;
import com.nsy.api.wms.request.product.SkuTagInfoRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductSkuPrintRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.business.base.constant.KafkaTopicConstant;
import com.nsy.business.base.enums.etl.BusinessTypeEnum;
import com.nsy.business.base.mq.EtlCommonMessage;
import com.nsy.wms.business.manage.amazon.AmazonApiService;
import com.nsy.wms.business.manage.amazon.request.AmazonSellerSkuRequest;
import com.nsy.wms.business.manage.amazon.response.AmazonSellerSku;
import com.nsy.wms.business.manage.amazon.response.AmazonSellerSkuResponse;
import com.nsy.wms.business.manage.product.ProductApiService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.internal.common.CommonBaseService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskItemService;
import com.nsy.wms.elasticjob.product.AmazonSellerSkuSyncDataJob;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.product.ProductStoreSkuMappingEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.product.ProductSpecInfoMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.WmsDateUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class ProductSpecInfoService extends ServiceImpl<ProductSpecInfoMapper, ProductSpecInfoEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSpecInfoService.class);

    @Inject
    private ProductApiService productApiService;
    @Inject
    private ProductLogService productLogService;
    @Autowired
    private AmazonApiService amazonApiService;
    @Autowired
    private ProductSpecInfoMapper productSpecInfoMapper;
    @Autowired
    private ProductStoreSkuMappingService storeSkuMappingService;
    @Autowired
    private PrintTemplateService printService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    private StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    private ProductWmsCategoryService wmsCategoryService;
    @Autowired
    private CommonBaseService commonBaseService;
    @Resource
    private BdTagMappingService tagMappingService;

    public ProductSpecInfo getByBarcode(String barcode) {
        ProductSpecInfoEntity topByBarcode = findTopByBarcode(barcode);
        if (Objects.isNull(topByBarcode)) {
            throw new BusinessServiceException("未查询到商品规格");
        }
        ProductSpecInfo productSpecInfo = new ProductSpecInfo();
        BeanUtilsEx.copyProperties(topByBarcode, productSpecInfo);
        return productSpecInfo;
    }

    @Transactional
    public void editPackageHeight(Integer id, BigDecimal packageHeight) {
        ProductSpecInfoEntity entity = this.getById(id);
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException(ProductInfoConstant.NOT_FOUND_PRODUCT_INFO);
        }
        if (packageHeight == null) {
            throw new BusinessServiceException("商品高度不允许为空！");
        }
        ProductInfo productInfo = productInfoService.getByProductId(entity.getProductId());
        //查找同类型产品
        List<ProductSpecInfoEntity> similarityProduct = this.getSimilarityProduct(entity.getProductId(), entity.getSize());
        //当查询出集合没有自身是进行添加
        if (null == similarityProduct) {
            similarityProduct = Collections.singletonList(entity);
        } else if (!similarityProduct.stream().anyMatch(detail -> id.equals(detail.getProductSpecInfoId()))) {
            similarityProduct.add(entity);
        }
        similarityProduct.forEach(
            detail -> {
                detail.setPackageHeight(packageHeight);
                //调用productapi同步修改商品库
                ProductSpecItem productSpecItem = new ProductSpecItem();
                productSpecItem.setProductSpecId(detail.getSpecId());
                productSpecItem.setHeight(packageHeight);
                productApiService.changeSkuHeight(productSpecItem);
                productLogService.saveProductSpecLog(detail.getProductId(), OperationEventEnum.EDIT_PACKAGE_HEIGHT, entity);
                sendEtlByPmsService(productInfo, detail.getSpecId().toString(), BusinessTypeEnum.PMS_LX_PRODUCT_SPEC, KafkaTopicConstant.PMD_LX_CREATE_STOCKIN_ORDER_TOPIC);
            }
        );
        this.updateBatchById(similarityProduct);
    }


    private void sendEtlByPmsService(ProductInfo productInfo, String businessNo, BusinessTypeEnum businessTypeEnum, String queueName) {
        if (Objects.equals(LocationEnum.TAILI.getCompanyCode(), productInfo.getCompanyCode()) || Objects.equals(LocationEnum.WEIYUE.getCompanyCode(), productInfo.getCompanyCode())) {
            EtlCommonMessage message = new EtlCommonMessage();
            message.setBusinessNo(businessNo);
            message.setSource(ServiceNameEnum.PMS_SERVICE.getCode());
            message.setLocation(LocationEnum.getLocationByCompanyCode(productInfo.getCompanyCode()));
            message.setBusinessType(businessTypeEnum.getCode());
            message.setBusinessMark(2);
            message.setCallbackInterfacePath(CommonBaseService.PROCESS_CUSTOMER_ORDER_CALLBACK_URL);
            commonBaseService.sendEtlCommonMessage(message, KafkaConstant.WMS_LINGXING_PRODUCT_INFO_TOPIC_NAME, queueName);
        }
    }

    @Transactional
    public void editWeightWithSimilarity(Integer id, BigDecimal weight) {
        ProductSpecInfoEntity entity = this.getById(id);
        if (Objects.isNull(entity))
            throw new BusinessServiceException(ProductInfoConstant.NOT_FOUND_PRODUCT_INFO);
        if (weight == null)
            throw new BusinessServiceException("商品重量不允许为空！");
        //查找同类型产品
        List<ProductSpecInfoEntity> similarityProduct = this.getSimilarityProduct(entity.getProductId(), entity.getSize());
        //当查询出集合没有自身是进行添加
        if (null == similarityProduct) {
            similarityProduct = Collections.singletonList(entity);
        } else if (!similarityProduct.stream().anyMatch(detail -> id.equals(detail.getProductSpecInfoId()))) {
            similarityProduct.add(entity);
        }
        ProductInfo productInfo = productInfoService.getByProductId(entity.getProductId());
        similarityProduct.forEach(
            detail -> {
                detail.setWeight(weight);
                detail.setActualWeight(weight);
                //调用productapi同步修改商品库
                ProductSpecItem productSpecItem = new ProductSpecItem();
                productSpecItem.setProductSpecId(detail.getSpecId());
                productSpecItem.setWeight(weight);
                productApiService.changeSkuWeight(productSpecItem);
                productLogService.saveProductSpecLog(detail.getProductId(), OperationEventEnum.EDIT_PACKAGE_WEIGHT, entity);
                sendEtlByPmsService(productInfo, detail.getSpecId().toString(), BusinessTypeEnum.PMS_LX_PRODUCT_SPEC, KafkaTopicConstant.PMD_LX_CREATE_STOCKIN_ORDER_TOPIC);
            }
        );
        this.updateBatchById(similarityProduct);
    }

    public void editWeight(Integer id, BigDecimal weight) {
        this.editWeightAndAuctualWeight(id, weight, weight);
    }

    public void editWeightAndAuctualWeight(Integer id, BigDecimal weight, BigDecimal auctualWeight) {
        ProductSpecInfoEntity entity = this.getById(id);
        if (Objects.isNull(entity))
            throw new BusinessServiceException(ProductInfoConstant.NOT_FOUND_PRODUCT_INFO);
        if (weight == null)
            throw new BusinessServiceException("商品重量不允许为空！");
        ProductInfo productInfo = productInfoService.getByProductId(entity.getProductId());
        entity.setWeight(weight);
        entity.setActualWeight(auctualWeight);
        //调用productapi同步修改商品库
        ProductSpecItem productSpecItem = new ProductSpecItem();
        productSpecItem.setProductSpecId(entity.getSpecId());
        productSpecItem.setWeight(weight);
        productApiService.changeSkuWeight(productSpecItem);
        productLogService.saveProductSpecLog(entity.getProductId(), OperationEventEnum.EDIT_PACKAGE_WEIGHT, entity);
        sendEtlByPmsService(productInfo, entity.getSpecId().toString(), BusinessTypeEnum.PMS_LX_PRODUCT_SPEC, KafkaTopicConstant.PMD_LX_CREATE_STOCKIN_ORDER_TOPIC);
        this.updateById(entity);
    }

    public void editHeight(Integer id, BigDecimal packageHeight) {
        ProductSpecInfoEntity entity = this.getById(id);
        if (Objects.isNull(entity))
            throw new BusinessServiceException(ProductInfoConstant.NOT_FOUND_PRODUCT_INFO);
        if (packageHeight == null)
            throw new BusinessServiceException("商品高度不允许为空！");
        ProductInfo productInfo = productInfoService.getByProductId(entity.getProductId());
        entity.setPackageHeight(packageHeight);
        //调用productapi同步修改商品库
        ProductSpecItem productSpecItem = new ProductSpecItem();
        productSpecItem.setProductSpecId(entity.getSpecId());
        productSpecItem.setHeight(packageHeight);
        productApiService.changeSkuHeight(productSpecItem);
        productLogService.saveProductSpecLog(entity.getProductId(), OperationEventEnum.EDIT_PACKAGE_HEIGHT, entity);
        sendEtlByPmsService(productInfo, entity.getSpecId().toString(), BusinessTypeEnum.PMS_LX_PRODUCT_SPEC, KafkaTopicConstant.PMD_LX_CREATE_STOCKIN_ORDER_TOPIC);
        this.updateById(entity);
    }

    public List<ProductSpecInfoEntity> findAllByProductId(Integer productId) {
        return this.list(new LambdaQueryWrapper<ProductSpecInfoEntity>().eq(ProductSpecInfoEntity::getProductId, productId));
    }

    public List<ProductSpecInfoEntity> findAllByProductIdList(List<Integer> productIdList) {
        return this.list(new LambdaQueryWrapper<ProductSpecInfoEntity>().in(ProductSpecInfoEntity::getProductId, productIdList));
    }

    public ProductSpecInfoEntity findTopBySpecId(Integer specId) {
        return this.getOne(new LambdaQueryWrapper<ProductSpecInfoEntity>().eq(ProductSpecInfoEntity::getSpecId, specId)
            .last("limit 1"));
    }

    public List<ProductSpecInfoEntity> findAllByProductIdAndColorCode(Integer productId, String colorCode) {
        LambdaQueryWrapper<ProductSpecInfoEntity> queryWrapper = new LambdaQueryWrapper<ProductSpecInfoEntity>().eq(ProductSpecInfoEntity::getProductId, productId)
            .eq(ProductSpecInfoEntity::getColorCode, colorCode);
        return this.list(queryWrapper);
    }

    public List<ProductSpecInfoEntity> findAllBySkc(String skc) {
        LambdaQueryWrapper<ProductSpecInfoEntity> queryWrapper = new LambdaQueryWrapper<ProductSpecInfoEntity>()
            .eq(ProductSpecInfoEntity::getSkc, skc);
        return this.list(queryWrapper);
    }

    public ProductSpecInfoEntity findTopByBarcode(String barcode) {
        //优先查找本地区的商品
        ProductSpecInfoEntity result = this.getBaseMapper().findByBarcodeAndLocation(barcode);

        if (Objects.isNull(result)) {
            result = this.findTopBySku(barcode);
            if (!Objects.isNull(result)) {
                return result;
            }
            result = findByStoreBarcode(barcode);
        }

        if (Objects.isNull(result))
            result = this.getOne(new QueryWrapper<ProductSpecInfoEntity>().lambda().eq(ProductSpecInfoEntity::getBarcode, barcode)
                .last("limit 1"));

        return result;
    }

    public ProductSpecInfoEntity findByStoreBarcode(String barcode) {
        List<ProductStoreSkuMappingEntity> skuMappingEntityList = storeSkuMappingService.list(new QueryWrapper<ProductStoreSkuMappingEntity>().lambda()
            .eq(ProductStoreSkuMappingEntity::getStoreBarcode, barcode));
        if (CollectionUtils.isEmpty(skuMappingEntityList)) {
            skuMappingEntityList = storeSkuMappingService.list(new QueryWrapper<ProductStoreSkuMappingEntity>().lambda()
                .eq(ProductStoreSkuMappingEntity::getStoreSku, barcode));
        }
        if (CollectionUtils.isEmpty(skuMappingEntityList)) {
            return null;
        }
        if (skuMappingEntityList.size() == 1)
            return this.findTopBySku(skuMappingEntityList.get(0).getSku());
        if (skuMappingEntityList.size() > 1) {
            //若存在多条映射关系，根据地区兼容
            List<ProductLocationInfo> productLocationInfos = storeSkuMappingService.getBaseMapper().findMappingLocationBySku(skuMappingEntityList.stream().map(ProductStoreSkuMappingEntity::getSku).collect(Collectors.toList()));
            Optional<ProductLocationInfo> locationInfoOptional = productLocationInfos.stream().filter(productLocationInfo -> TenantContext.getTenant().equalsIgnoreCase(productLocationInfo.getLocation())).findAny();
            if (locationInfoOptional.isPresent())
                return this.findTopBySku(locationInfoOptional.get().getSku());

            //没有该地区的映射 优先取泉州地区
            Optional<ProductLocationInfo> locationInfoOptionalQuanzhou = productLocationInfos.stream().filter(productLocationInfo -> LocationEnum.QUANZHOU.name().equalsIgnoreCase(productLocationInfo.getLocation())).findAny();
            if (locationInfoOptionalQuanzhou.isPresent())
                return this.findTopBySku(locationInfoOptionalQuanzhou.get().getSku());

            //取第一个
            return this.findTopBySku(skuMappingEntityList.get(0).getSku());
        }
        return null;
    }

    public ProductSpecInfoEntity findTopByBarcodeFromShelve(String barcode, Integer taskId) {
        //如果是伟跃，如果存在sellersku和sellerBarcode,只判断入库单明细的sellersku
        if (LocationEnum.WEIYUE.name().equals(TenantContext.getTenant())) {
            List<StockinShelveTaskItemEntity> list = stockinShelveTaskItemService.list(new LambdaQueryWrapper<StockinShelveTaskItemEntity>()
                .select(StockinShelveTaskItemEntity::getSellerBarcode, StockinShelveTaskItemEntity::getSellerSku, StockinShelveTaskItemEntity::getSku)
                .eq(StockinShelveTaskItemEntity::getShelveTaskId, taskId));
            if (list.stream().anyMatch(item -> StringUtils.hasText(item.getSellerBarcode()))) {
                Optional<StockinShelveTaskItemEntity> any = list.stream().filter(item -> barcode.equalsIgnoreCase(item.getSellerBarcode()) || barcode.equalsIgnoreCase(item.getSellerSku())).findAny();
                StockinShelveTaskItemEntity itemEntity = any.orElseThrow(() -> new BusinessServiceException("商品条码不存在或不在该入库任务中"));
                return this.findTopBySku(itemEntity.getSku());
            }
        }
        //优先查找本地区的商品
        ProductSpecInfoEntity result = this.getBaseMapper().findByBarcodeAndLocation(barcode);

        if (Objects.isNull(result))
            result = findStockinShelveTaskItemByBarcode(barcode, taskId);

        if (Objects.isNull(result)) {
            //查询店铺条码
            result = findByStoreBarcode(barcode);
            if (Objects.nonNull(result))
                return result;
        }
        if (Objects.isNull(result))
            result = this.getOne(new QueryWrapper<ProductSpecInfoEntity>().lambda().eq(ProductSpecInfoEntity::getBarcode, barcode).last("limit 1"));

        if (Objects.isNull(result))
            throw new BusinessServiceException("商品条码不存在或不在该上架任务中");
        return result;
    }

    private ProductSpecInfoEntity findStockinShelveTaskItemByBarcode(String barcode, Integer taskId) {
        StockinShelveTaskItemEntity itemEntity = stockinShelveTaskItemService.getOne(new LambdaQueryWrapper<StockinShelveTaskItemEntity>().eq(StockinShelveTaskItemEntity::getShelveTaskId, taskId)
            .eq(StockinShelveTaskItemEntity::getSellerBarcode, barcode).last("limit 1"));
        if (Objects.isNull(itemEntity)) {
            itemEntity = stockinShelveTaskItemService.getOne(new LambdaQueryWrapper<StockinShelveTaskItemEntity>().eq(StockinShelveTaskItemEntity::getShelveTaskId, taskId)
                .eq(StockinShelveTaskItemEntity::getSellerSku, barcode).last("limit 1"));
        }
        return Objects.nonNull(itemEntity) ? this.findTopBySku(itemEntity.getSku()) : null;
    }

    public ProductSpecInfoEntity findTopByBarcodeFromStockin(String barcode, List<Integer> taskIds) {

        //如果是伟跃，只判断入库单明细的sellersku
        if (LocationEnum.WEIYUE.name().equals(TenantContext.getTenant())) {
            List<StockinOrderTaskItemEntity> list = stockinOrderTaskItemService.list(new LambdaQueryWrapper<StockinOrderTaskItemEntity>()
                .select(StockinOrderTaskItemEntity::getSellerBarcode, StockinOrderTaskItemEntity::getSellerSku, StockinOrderTaskItemEntity::getSku)
                .in(StockinOrderTaskItemEntity::getTaskId, taskIds));
            if (list.stream().anyMatch(item -> StringUtils.hasText(item.getSellerBarcode()))) {
                Optional<StockinOrderTaskItemEntity> any = list.stream().filter(item -> barcode.equalsIgnoreCase(item.getSellerBarcode()) || barcode.equalsIgnoreCase(item.getSellerSku())).findAny();
                StockinOrderTaskItemEntity itemEntity = any.orElseThrow(() -> new BusinessServiceException("商品条码不存在或不在该入库任务中"));
                return this.findTopBySku(itemEntity.getSku());
            }
        }
        //优先查找本地区的商品
        ProductSpecInfoEntity result = this.getBaseMapper().findByBarcodeAndLocation(barcode);
        if (Objects.isNull(result))
            result = findStockinOrderItemByBarcode(barcode, taskIds);
        if (Objects.isNull(result)) {
            //查询店铺条码
            result = findByStoreBarcode(barcode);
            if (Objects.nonNull(result))
                return result;
        }
        if (Objects.isNull(result))
            result = this.getOne(new QueryWrapper<ProductSpecInfoEntity>().lambda().eq(ProductSpecInfoEntity::getBarcode, barcode).last("limit 1"));

        if (Objects.isNull(result))
            throw new BusinessServiceException("商品条码不存在或不在该入库任务中");
        return result;
    }

    /**
     * 查询入库单明细
     */
    private ProductSpecInfoEntity findStockinOrderItemByBarcode(String barcode, List<Integer> taskIds) {
        StockinOrderTaskItemEntity itemEntity = stockinOrderTaskItemService.getOne(new LambdaQueryWrapper<StockinOrderTaskItemEntity>()
            .in(StockinOrderTaskItemEntity::getTaskId, taskIds)
            .and(query -> {
                query.eq(StockinOrderTaskItemEntity::getSellerBarcode, barcode)
                    .or()
                    .eq(StockinOrderTaskItemEntity::getSellerSku, barcode);
            })
            .last("limit 1"));
        if (Objects.isNull(itemEntity)) {
            itemEntity = stockinOrderTaskItemService.getOne(new LambdaQueryWrapper<StockinOrderTaskItemEntity>()
                .in(StockinOrderTaskItemEntity::getTaskId, taskIds)
                .and(query -> {
                    query.eq(StockinOrderTaskItemEntity::getBarcode, barcode)
                        .or()
                        .eq(StockinOrderTaskItemEntity::getSku, barcode);
                })
                .last("limit 1"));
        }
        return Objects.nonNull(itemEntity) ? this.findTopBySku(itemEntity.getSku()) : null;
    }

    public List<ProductSpecInfoEntity> findAllBySpecIdIn(List<Integer> specIds) {
        if (CollectionUtils.isEmpty(specIds)) {
            return Lists.newArrayList();
        }
        return this.list(new LambdaQueryWrapper<ProductSpecInfoEntity>().in(ProductSpecInfoEntity::getSpecId, specIds));
    }

    public ProductSpecInfoEntity findTopBySku(String sku) {
        return this.getOne(new QueryWrapper<ProductSpecInfoEntity>().lambda().eq(ProductSpecInfoEntity::getSku, sku)
            .last("limit 1"));
    }

    public ProductSpecInfo getBySku(String sku) {
        ProductSpecInfoEntity topBySku = this.getOne(new QueryWrapper<ProductSpecInfoEntity>().lambda().eq(ProductSpecInfoEntity::getSku, sku)
            .last("limit 1"));
        if (Objects.isNull(topBySku)) {
            throw new BusinessServiceException(String.format("未查询到商品规格 %s", sku));
        }
        ProductSpecInfo productSpecInfo = new ProductSpecInfo();
        BeanUtilsEx.copyProperties(topBySku, productSpecInfo);
        return productSpecInfo;
    }

    public List<ProductSpecInfoEntity> findAllBySkuIn(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Lists.newArrayList();
        }
        return this.list(new LambdaQueryWrapper<ProductSpecInfoEntity>().in(ProductSpecInfoEntity::getSku, skuList));
    }

    public Map<String, ProductSpecInfoEntity> findAllMapBySkuIn(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }
        return findAllBySkuIn(skuList).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity(), (k1, k2) -> k1));
    }

    @org.springframework.transaction.annotation.Transactional(propagation = Propagation.REQUIRES_NEW)
    public void syncSkuList(List<String> skuList, List<ProductSpecInfo> specInfoEntityList, List<ProductStoreSkuMappingEntity> mappingEntityList) {
        if (CollUtil.isEmpty(specInfoEntityList)) {
            return;
        }
        AmazonSellerSkuRequest amazonSellerSkuRequest = new AmazonSellerSkuRequest();
        amazonSellerSkuRequest.setErpSkus(skuList);
        AmazonSellerSkuResponse response = amazonApiService.getAmazonSellerSku(amazonSellerSkuRequest);
        if (response != null && !response.getAmazonSellerSkus().isEmpty()) {
            this.dealSync(response.getAmazonSellerSkus(), specInfoEntityList, mappingEntityList);
        }
    }

    /**
     * 同步处理
     */
    public void dealSync(List<AmazonSellerSku> amazonSellerSkus, List<ProductSpecInfo> specInfoEntityList, List<ProductStoreSkuMappingEntity> mappingEntityList) {
        List<ProductStoreSkuMappingEntity> updateList = new LinkedList<>();
        List<ProductStoreSkuMappingEntity> insertList = new LinkedList<>();
        List<Integer> deleteList = new LinkedList<>();
        HashMap<String, Integer> sellerSkuMap = new HashMap<>();
        for (AmazonSellerSku sellerSku : amazonSellerSkus) {
            Optional<ProductSpecInfo> spec = specInfoEntityList.stream().filter(t -> StringUtils.hasText(t.getSku()) && t.getSku().equals(sellerSku.getRealErpSku())).findFirst();
            if (!spec.isPresent())
                continue;
            if (sellerSkuMap.containsKey(sellerSku.getSellerSku()))
                continue;
            sellerSkuMap.put(sellerSku.getSellerSku(), spec.get().getSpecId());
            ProductStoreSkuMappingEntity mappingEntity = mappingEntityList.stream().filter(o -> o.getSku().equals(spec.get().getSku())
                && o.getStoreId().equals(sellerSku.getSkuAmazonStoreId())
                && o.getStoreSku().equals(sellerSku.getSellerSku())).findFirst().orElse(null);
            if (mappingEntity == null) {
                mappingEntity = new ProductStoreSkuMappingEntity();
                mappingEntity.setSpecId(spec.get().getSpecId());
                mappingEntity.setSku(spec.get().getSku());
                mappingEntity.setBarcode(spec.get().getBarcode());
                setBaseInfo(mappingEntity, sellerSku);
                insertList.add(mappingEntity);
            } else if (!StringUtils.hasText(sellerSku.getFnSku())) {
                deleteList.add(mappingEntity.getMappingId());
            } else {
                setBaseInfo(mappingEntity, sellerSku);
                updateList.add(mappingEntity);
            }
        }
        // 新增过滤 storeBarcode 为空的数据
        insertList = insertList.stream().filter(o -> StringUtils.hasText(o.getStoreBarcode())).collect(Collectors.toList());
        LOGGER.info("insertList: {} , updateList: {} , deleteList: {} ", insertList.size(), updateList.size(), deleteList.size());
        if (!insertList.isEmpty())
            storeSkuMappingService.saveBatch(insertList);
        if (!updateList.isEmpty())
            storeSkuMappingService.updateBatchById(updateList);
        if (!deleteList.isEmpty())
            storeSkuMappingService.removeByIds(deleteList);
    }

    private void setBaseInfo(ProductStoreSkuMappingEntity mappingEntity, AmazonSellerSku sellerSku) {
        mappingEntity.setStoreId(sellerSku.getSkuAmazonStoreId());
        mappingEntity.setStoreName(sellerSku.getSkuStoreName());
        mappingEntity.setStoreSku(sellerSku.getSellerSku());
        mappingEntity.setStoreBarcode(sellerSku.getFnSku());
        mappingEntity.setSkuRule(sellerSku.getSkuRule());
        mappingEntity.setTitle(sellerSku.getTitle());
        mappingEntity.setAsin(sellerSku.getAsin());
        mappingEntity.setIsPushWcs(0);
        mappingEntity.setCreateBy(AmazonSellerSkuSyncDataJob.JOB_NAME);
    }

    public PrintListResponse printProductBarcode(StockinReturnProductSkuPrintRequest request) {
        Map<String, ProductSpecInfoEntity> collect = findAllBySkuIn(request.getSkuList().stream().map(StockinReturnProductSkuPrintRequest.Item::getSku).collect(Collectors.toList()))
            .stream().collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        List<String> htmlList = new ArrayList<>();
        PrintListResponse response = new PrintListResponse();
        for (StockinReturnProductSkuPrintRequest.Item item : request.getSkuList()) {
            ProductSpecInfoEntity specInfoEntity = collect.get(item.getSku());
            if (specInfoEntity == null) {
                continue;
            }
            StockinReturnProductSkuPrintDTO printDetail = new StockinReturnProductSkuPrintDTO();
            BeanUtils.copyProperties(specInfoEntity, printDetail, "version");
            printDetail.setSupplierName(item.getSupplierName());
            printDetail.setVersionCode(item.getVersionCode());
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), printDetail);
            for (int i = 0; i < item.getQty(); i++) {
                htmlList.add(transfer);
            }
        }
        response.setHtmlList(PrintTransferUtils.doubleTransfer(request.getSinglePrint(), htmlList, templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    public PrintListResponse printProductBarcodeSpot(SkuPrintSpotRequest request) {
        List<SkuPrint> skuList = request.getSkuList();
        Map<String, Integer> skuMap = skuList.stream().collect(Collectors.toMap(SkuPrint::getSku, SkuPrint::getQty));
        Map<String, ProductSpecInfoEntity> collect = findAllBySkuIn(skuList.stream().map(SkuPrint::getSku).collect(Collectors.toList())).stream()
            .collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        List<String> htmlList = new ArrayList<>();
        PrintListResponse response = new PrintListResponse();
        List<SelectTreeModel> categorySelect = wmsCategoryService.getCategorySelect();
        Set<Integer> allIds = new HashSet<>();
        categorySelect.stream().filter(item -> item.getValue() != null && item.getLabel().contains("鞋子")).findFirst().ifPresent(category -> {
            allIds.add(category.getId());
            getShoesIdList(allIds, category.getChildren());
        });
        for (Map.Entry<String, Integer> sku : skuMap.entrySet()) {
            ProductSpecInfoEntity specInfoEntity = collect.get(sku.getKey());
            if (specInfoEntity == null) {
                continue;
            }
            ProductInfoEntity product = productInfoService.findTopByProductId(specInfoEntity.getProductId());
            if (product != null && product.getCategoryId() != null && allIds.contains(product.getCategoryId())) {
                specInfoEntity.setSize("");
            }

            Map<String, Object> objectMap = BeanUtil.beanToMap(specInfoEntity, false, true);
            // 手动将 Map<String, Object> 转换为 Map<String, String>
            Map<String, String> stringMap = objectMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().toString()));
            stringMap.put("brandShortName", request.getBrandShortName());
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), stringMap);
            for (int i = 0; i < sku.getValue(); i++) {
                htmlList.add(transfer);
            }
        }
        response.setHtmlList(PrintTransferUtils.doubleTransfer(request.getSinglePrint(), htmlList, templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    public PrintListResponse printProductBarcodeSpotNew(SkuPrintSpotNewRequest request) {
        List<SkuPrint> skuList = request.getSkuList().stream().sorted(Comparator.comparing(SkuPrint::getSortNum)).collect(Collectors.toList());
        Collections.reverse(skuList);
        Map<String, ProductSpecInfoEntity> collect = findAllBySkuIn(skuList.stream().map(SkuPrint::getSku).collect(Collectors.toList())).stream()
            .collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        List<String> htmlList = new ArrayList<>();
        PrintListResponse response = new PrintListResponse();
        String format = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        List<SelectTreeModel> categorySelect = wmsCategoryService.getCategorySelect();
        Set<Integer> allIds = new HashSet<>();
        categorySelect.stream().filter(item -> item.getValue() != null && item.getLabel().contains("鞋子")).findFirst().ifPresent(category -> {
            allIds.add(category.getId());
            getShoesIdList(allIds, category.getChildren());
        });
        for (SkuPrint sku : skuList) {
            ProductSpecInfoEntity specInfoEntity = collect.get(sku.getSku());
            if (specInfoEntity == null) {
                continue;
            }
            StockinSpotSkuPrint stockinSpotSkuPrint = new StockinSpotSkuPrint();
            BeanUtilsEx.copyProperties(specInfoEntity, stockinSpotSkuPrint);
            stockinSpotSkuPrint.setSupplierName(sku.getSupplierName());
            stockinSpotSkuPrint.setNow(format);
            ProductInfoEntity product = productInfoService.findTopByProductId(specInfoEntity.getProductId());
            if (product != null && product.getCategoryId() != null && allIds.contains(product.getCategoryId())) {
                stockinSpotSkuPrint.setSize("");
            }
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), stockinSpotSkuPrint);
            for (int i = 0; i < sku.getQty(); i++) {
                htmlList.add(transfer);
            }
        }
        response.setHtmlList(PrintTransferUtils.doubleTransfer(request.getSinglePrint(), htmlList, templateEntity));
        if (StringUtils.hasText(request.getLogisticsNo())) {
            // 有物流单号，那就一并打印物流单
            PrintTemplateEntity temp = printService.getByName(PrintTemplateNameEnum.SPOT_LOGISTICS_NO.getTemplateName());
            String transfer = PrintTransferUtils.transfer(temp.getContent(), request);
            response.getHtmlList().add(transfer);
        }
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    public static void getShoesIdList(Set<Integer> ids, List<SelectTreeModel> categorySelect) {
        if (!CollectionUtils.isEmpty(categorySelect)) {
            categorySelect.forEach(item -> {
                ids.add(item.getId());
                getShoesIdList(ids, item.getChildren());
            });
        }
    }

    /**
     * spec id集合获取商品信息集合
     */
    public List<ProductSpecInfoEntity> listBySpecIdList(List<Integer> specIdList) {
        QueryWrapper<ProductSpecInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ProductSpecInfoEntity::getSpecId, specIdList);
        return list(queryWrapper);
    }

    /**
     * 校验信息，如果有错误则返回错误信息
     *
     * @param sku
     * @param bdPositionEntity
     * @return
     */
    public String validPriceBySku(String sku, BdPositionEntity bdPositionEntity) {
        ProductSpecInfoEntity topBySku = this.findTopBySku(sku);
        ProductInfoEntity topByProductId = productInfoService.findTopByProductId(topBySku.getProductId());
        if (Objects.isNull(topBySku.getActualWeight()) || topBySku.getActualWeight().compareTo(BigDecimal.ZERO) == 0) {
            ProductSpec productSpecDetail = productApiService.getProductSpecDetail(topBySku.getSpecId());
            if (Objects.isNull(productSpecDetail) || Objects.isNull(productSpecDetail.getWeight()) || productSpecDetail.getWeight().compareTo(BigDecimal.ZERO) == 0) {
                return topBySku.getSku() + "该商品没有称重！";
            } else {
                topBySku.setActualWeight(productSpecDetail.getWeight());
                this.updateById(topBySku);
            }
        }
        //左海仓且为pack商品不校验成分
        if (!Objects.isNull(bdPositionEntity) && SpaceAreaMapConstant.WmsSpace.ZUOHAI_SPACE.equals(bdPositionEntity.getSpaceName())
            && org.apache.commons.lang3.StringUtils.startsWithIgnoreCase(sku, "pack")) {
            return null;
        }
        if (!StringUtils.hasText(topByProductId.getFabricType())) {
            ProductFabricTypeInfo productDetail = productApiService.getProductFabricTypeInfo(topByProductId.getProductId());
            if (Objects.isNull(productDetail) || !StringUtils.hasText(productDetail.getFabricType())) {
                return topBySku.getSku() + "该商品没有成分！";
            } else {
                topByProductId.setFabricType(productDetail.getFabricType());
                productInfoService.updateById(topByProductId);
            }
        }
        return null;
    }

    // 校验重量、成分、以及采购价
    public void validPrice(String sku) {
        ProductSpecInfoEntity topBySku = this.findTopBySku(sku);
        ProductInfoEntity topByProductId = productInfoService.findTopByProductId(topBySku.getProductId());
        if ((LocationEnum.GUANGZHOU.name().equals(TenantContext.getTenant()) || LocationEnum.XIAMEN.name().equals(TenantContext.getTenant()))
            && (Objects.isNull(topBySku.getActualWeight()) || topBySku.getActualWeight().compareTo(BigDecimal.ZERO) == 0)) {
            ProductSpec productSpecDetail = productApiService.getProductSpecDetail(topBySku.getSpecId());
            if (Objects.isNull(productSpecDetail) || Objects.isNull(productSpecDetail.getWeight()) || productSpecDetail.getWeight().compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessServiceException(topBySku.getSku() + "该商品没有称重！");
            } else {
                topBySku.setActualWeight(productSpecDetail.getWeight());
                this.updateById(topBySku);
            }
        }
        if (!StringUtils.hasText(topByProductId.getFabricType())) {
            ProductFabricTypeInfo productDetail = productApiService.getProductFabricTypeInfo(topByProductId.getProductId());
            if (Objects.isNull(productDetail) || !StringUtils.hasText(productDetail.getFabricType())) {
                throw new BusinessServiceException(topBySku.getSku() + "该商品没有成分！");
            } else {
                topByProductId.setFabricType(productDetail.getFabricType());
                productInfoService.updateById(topByProductId);
            }
        }
    }

    public BigDecimal getWeight(String sku, Integer qty) {
        ProductSpecInfoEntity specInfo = findTopBySku(sku);
        if (Objects.isNull(specInfo))
            throw new BusinessServiceException(String.format("%s 的 specInfo 不存在", sku));
        return ProductSpecInfoService.getSkuWeightDefault(specInfo).multiply(new BigDecimal(qty));
    }

    public static BigDecimal getSkuWeightDefault(ProductSpecInfoEntity entity) {
        if (entity == null)
            throw new BusinessServiceException("无法找单商品明细，请核对");
        BigDecimal decimal = entity.getActualWeight() == null || entity.getActualWeight().compareTo(BigDecimal.ZERO) == 0 ? entity.getWeight() : entity.getActualWeight();
        if (decimal == null) {
            return BigDecimal.ZERO;
        }
        return decimal;
    }

    public PageResponse<ProductSku> productSkuList(ProductSkuRequest request) {
        PageResponse<ProductSku> pageResponse = new PageResponse<>();
        if (!StringUtils.hasText(request.getSku()) && !StringUtils.hasText(request.getBarcode())) {
            throw new BusinessServiceException("规格编码和条形码不能同时为空");
        }
        IPage<ProductSku> page = productSpecInfoMapper.selectProductSpecInfoBySku(new Page(request.getPageIndex(), request.getPageSize()), request.getSku(), request.getBarcode());

        pageResponse.setContent(page.getRecords());
        pageResponse.setTotalCount(page.getTotal());

        return pageResponse;
    }

    public PrintListResponse printProductBarcodeSupplier(SkuPrintSpotRequest request) {
        List<SkuPrint> skuList = request.getSkuList().stream().sorted(Comparator.comparing(SkuPrint::getSortNum)).collect(Collectors.toList());
        Collections.reverse(skuList);
        Map<String, ProductSpecInfoEntity> collect = findAllBySkuIn(skuList.stream().map(SkuPrint::getSku).collect(Collectors.toList())).stream()
            .collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        List<String> htmlList = new ArrayList<>();
        PrintListResponse response = new PrintListResponse();
        for (SkuPrint sku : skuList) {
            ProductSpecInfoEntity specInfoEntity = collect.get(sku.getSku());
            if (specInfoEntity == null) {
                continue;
            }
            ProductSpecPrint skuPrint = new ProductSpecPrint();
            BeanUtilsEx.copyProperties(specInfoEntity, skuPrint);
            skuPrint.setSupplierName(sku.getSupplierName());
            skuPrint.setVersionCode(request.getVersionCode());
            skuPrint.setBrandShortName(request.getBrandShortName());
            if (request.getPrintDate() != null) {
                skuPrint.setDateCode(WmsDateUtils.convertYearToLetter(DateUtil.year(request.getPrintDate())) + DateUtil.format(request.getPrintDate(), "MMdd"));
            } else {
                skuPrint.setDateCode("");
            }
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), skuPrint);
            for (int i = 0; i < sku.getQty(); i++) {
                htmlList.add(transfer);
            }
        }
        response.setHtmlList(PrintTransferUtils.doubleTransfer(request.getSinglePrint(), htmlList, templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    public List<ProductSpecInfoEntity> getListBySkuList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList))
            return new ArrayList<>();
        return this.list(new LambdaQueryWrapper<ProductSpecInfoEntity>().in(ProductSpecInfoEntity::getSku, skuList));
    }

    public List<ProductSpecInfo> getProductInfoBySku(List<String> skuList) {
        List<ProductSpecInfoEntity> productSpecInfoList = this.list(new LambdaQueryWrapper<ProductSpecInfoEntity>().in(ProductSpecInfoEntity::getSku, skuList));
        if (CollectionUtils.isEmpty(productSpecInfoList)) {
            return Collections.emptyList();
        }
        List<ProductSpecInfo> productSpecInfos = new ArrayList<>();
        productSpecInfoList.stream().forEach(detail -> {
            ProductSpecInfo specInfo = new ProductSpecInfo();
            BeanUtils.copyProperties(detail, specInfo);
            productSpecInfos.add(specInfo);
        });
        return productSpecInfos;
    }

    public List<ProductSpecInfo> productSkuTagList(SkuTagInfoRequest request) {
        List<ProductSpecInfoEntity> list = this.list(new LambdaQueryWrapper<ProductSpecInfoEntity>().in(ProductSpecInfoEntity::getSku, request.getSkuList()));
        Map<String, List<String>> productTagMap = tagMappingService.getProductTagBySkus(request.getSkuList());
        List<ProductSpecInfo> resultList = new ArrayList<>();
        list.forEach(entity -> {
            ProductSpecInfo specInfo = new ProductSpecInfo();
            ProductInfoEntity productEntity = productInfoService.findTopByProductId(entity.getProductId());
            BeanUtilsEx.copyProperties(entity, specInfo);
            specInfo.setProductTag(productTagMap.getOrDefault(entity.getSku(), Collections.emptyList()));
            specInfo.setPackageVacuum(productEntity.getPackageVacuum());
            resultList.add(specInfo);
        });
        return resultList;
    }

    /**
     * 获取当前商品同规格、同尺码，不同颜色重量（先取实际重量，如都无则取预估重量）
     */
    public BigDecimal getSimilarityWeight(Integer specId) {
        LambdaQueryWrapper<ProductSpecInfoEntity> queryQuery = new LambdaQueryWrapper<>();
        queryQuery.eq(ProductSpecInfoEntity::getSpecId, specId);
        ProductSpecInfoEntity entity = this.getOne(queryQuery);
        if (Objects.isNull(entity))
            return BigDecimal.ZERO;
        BigDecimal actualWeight = productSpecInfoMapper.selectSimilarityActualWeight(entity.getProductId(), entity.getSize());
        if (Objects.nonNull(actualWeight) && BigDecimal.ZERO.compareTo(actualWeight) != 0) {
            return actualWeight;
        }
        BigDecimal weight = productSpecInfoMapper.selectSimilarityWeight(entity.getProductId(), entity.getSize());
        if (Objects.isNull(weight)) {
            return BigDecimal.ZERO;
        }
        return weight;
    }

    @Transactional
    public void editPackageBatch(ProductSpecPackageBatchRequest request) {
        request.getBatchList().forEach(item -> {
            try {
                ProductSpecInfoEntity specInfo = this.getOne(new LambdaQueryWrapper<ProductSpecInfoEntity>()
                    .select(ProductSpecInfoEntity::getProductSpecInfoId)
                    .eq(ProductSpecInfoEntity::getSku, item.getSku()));
                if (Objects.nonNull(specInfo)) {
                    if (Objects.nonNull(item.getPackageHeight()))
                        this.editPackageHeight(specInfo.getProductSpecInfoId(), item.getPackageHeight());
                    if (Objects.nonNull(item.getWeight()))
                        this.editWeightWithSimilarity(specInfo.getProductSpecInfoId(), item.getWeight());
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        });
    }

    /**
     * 查找相同类型商品、同尺码、不同颜色商品集合，新增逻辑无论先前是否存在重量都覆盖
     */
    public List<ProductSpecInfoEntity> getSimilarityProduct(Integer productId, String size) {
        if (!Objects.nonNull(productId) || !StringUtils.hasText(size)) {
            return null;
        }
        LambdaQueryWrapper<ProductSpecInfoEntity> queryQuery = new LambdaQueryWrapper<>();
        queryQuery.eq(ProductSpecInfoEntity::getProductId, productId);
        queryQuery.eq(ProductSpecInfoEntity::getSize, size);
        return this.list(queryQuery);
    }

    public BigDecimal getSpuWeight(Integer productId) {
        if (!Objects.nonNull(productId)) {
            return null;
        }
        LambdaQueryWrapper<ProductSpecInfoEntity> queryQuery = new LambdaQueryWrapper<>();
        queryQuery.eq(ProductSpecInfoEntity::getProductId, productId)
            .and(qw -> qw.isNotNull(ProductSpecInfoEntity::getActualWeight).or().isNotNull(ProductSpecInfoEntity::getWeight));
        queryQuery.last(MybatisQueryConstant.QUERY_FIRST);
        ProductSpecInfoEntity one = this.getOne(queryQuery);
        if (one == null) {
            return null;
        }
        return one.getActualWeight() == null ? one.getWeight() : one.getActualWeight();
    }

    public Integer getProductSpecIdBySku(String sku) {
        return this.baseMapper.getProductSpecIdBySku(sku);
    }

    // 根据商品skuList查询对应的商品信息
    public ProductSkuInfoMap productSkuInfoList(SkuInfoListRequest request) {
        ProductSkuInfoMap map = new ProductSkuInfoMap();
        List<ProductSpecInfoEntity> listBySkuList = getListBySkuList(request.getSkuList());
        listBySkuList.forEach(item -> {
            ProductSpecInfo spec = new ProductSpecInfo();
            BeanUtilsEx.copyProperties(item, spec);
            spec.setWeight(NumberUtil.div(spec.getWeight(), new BigDecimal("1000"), 3));
            spec.setActualWeight(NumberUtil.div(spec.getActualWeight(), new BigDecimal("1000"), 3));
            map.getSkuInfoMap().put(item.getSku(), spec);
        });
        return map;
    }

    /**
     * 根据sku或者barcode查找信息
     *
     * @param skuBarcode
     * @return
     */
    public ProductSpecInfoEntity getInfoBySkuBarcode(String skuBarcode) {
        if (!StringUtils.hasText(skuBarcode)) {
            return null;
        }
        LambdaQueryWrapper<ProductSpecInfoEntity> queryQuery = new LambdaQueryWrapper<>();
        queryQuery.eq(ProductSpecInfoEntity::getSku, skuBarcode);
        queryQuery.last("limit 1");
        ProductSpecInfoEntity skuInfo = this.getOne(queryQuery);
        if (Objects.nonNull(skuInfo)) {
            return skuInfo;
        }
        queryQuery.clear();
        queryQuery.eq(ProductSpecInfoEntity::getBarcode, skuBarcode);
        queryQuery.last("limit 1");
        return this.getOne(queryQuery);
    }

    public ProductSpecInfoEntity findByStoreStockin(String codeOrSku) {
        StockinOrderTaskItemEntity itemEntity = stockinOrderTaskItemService.getOne(new LambdaQueryWrapper<StockinOrderTaskItemEntity>()
            .eq(StockinOrderTaskItemEntity::getSellerBarcode, codeOrSku)
            .orderByDesc(StockinOrderTaskItemEntity::getTaskItemId)
            .last("limit 1"));
        if (Objects.isNull(itemEntity)) {
            itemEntity = stockinOrderTaskItemService.getOne(new LambdaQueryWrapper<StockinOrderTaskItemEntity>()
                .eq(StockinOrderTaskItemEntity::getSellerSku, codeOrSku)
                .orderByDesc(StockinOrderTaskItemEntity::getTaskItemId)
                .last("limit 1"));
        }
        if (Objects.isNull(itemEntity)) {
            return null;
        }
        return this.findTopBySku(itemEntity.getSku());
    }
}
