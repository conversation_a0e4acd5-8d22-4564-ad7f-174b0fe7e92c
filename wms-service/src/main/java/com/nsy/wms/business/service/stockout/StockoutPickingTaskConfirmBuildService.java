package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.bd.BdSpaceArea;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stockin.StockoutPickingTaskUpdateBatchStatusMessage;
import com.nsy.api.wms.domain.stockout.StockoutOrderSkuDescription;
import com.nsy.api.wms.domain.stockout.StockoutOrderTaskItemDetail;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskConfirmSku;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskConfirmSpaceArea;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskConfirmSpaceAreaSku;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.bd.BdSpaceAreaService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingItemRecordEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.utils.mp.TenantContext;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class StockoutPickingTaskConfirmBuildService {

    @Autowired
    StockoutPickingTaskService stockoutPickingTaskService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    BdSpaceAreaService spaceAreaService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    ProductInfoService productInfoService;
    @Resource
    BdTagMappingService tagMappingService;

    //isSync 是否同步更新波次状态
    public void startPickingUpdateStatus(StockoutPickingTaskEntity taskEntity, StockInternalBox stockInternalBox, boolean isSync) {
        taskEntity.setReceiveBoxNo(stockInternalBox.getInternalBoxCode());
        taskEntity.setUpdateBy(loginInfoService.getName());
        taskEntity.setOperator(loginInfoService.getName());
        taskEntity.setOperateStartDate(new Date());
        taskEntity.setStatus(StockoutPickingTaskStatusEnum.PICKING.name());
        stockoutPickingTaskService.updateById(taskEntity);
        //缺货拣货不更新波次单和出库单状态
        StockoutBatchEntity batchEntity = stockoutBatchService.getById(taskEntity.getBatchId());
        if (StockoutWavePlanTypeEnum.LACK_WAVE.name().equals(batchEntity.getBatchType())) {
            updateBatchStatus(taskEntity.getTaskId(), taskEntity.getBatchId(), stockInternalBox.getInternalBoxCode());
        }
        if (!StockoutPickingTaskTypeEnum.OUT_STOCK_PICKING.name().equals(taskEntity.getTaskType())) {
            if (isSync) {
                updateBatchStatus(taskEntity.getTaskId(), taskEntity.getBatchId(), stockInternalBox.getInternalBoxCode());
            } else {
                StockoutPickingTaskUpdateBatchStatusMessage stockoutPickingTaskUpdateBatchStatusMessage = new StockoutPickingTaskUpdateBatchStatusMessage(taskEntity.getBatchId(), taskEntity.getTaskId(), stockInternalBox.getInternalBoxCode());
                LocationWrapperMessage locationWrapperMessage = new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), stockoutPickingTaskUpdateBatchStatusMessage);
                messageProducer.sendMessage(KafkaConstant.STOCKOUT_PICKING_TASK_UPDATE_BATCH_TOPIC_NAME, KafkaConstant.STOCKOUT_PICKING_TASK_UPDATE_BATCH_TOPIC, locationWrapperMessage);
            }
        }
    }

    public void updateBatchStatus(Integer taskId, Integer batchId, String internalBoxCode) {
        List<StockoutBatchEntity> stockoutBatchEntityList = new ArrayList<>(8);
        StockoutBatchEntity stockoutBatchEntity = stockoutBatchService.getById(batchId);
        if (StockoutWaveTaskStatusEnum.WAIT_PICK.name().equals(stockoutBatchEntity.getStatus()) || StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name().equals(stockoutBatchEntity.getStatus()) || StockoutWaveTaskStatusEnum.NEW.name().equals(stockoutBatchEntity.getStatus())) {
            stockoutBatchEntity.setUpdateBy(loginInfoService.getName());
            stockoutBatchEntity.setStatus(StockoutWaveTaskStatusEnum.PICKING.name());
            stockoutBatchEntityList.add(stockoutBatchEntity);
        }
        //更新子波次
        if (Objects.nonNull(stockoutBatchEntity.getIsMergeBatch()) && stockoutBatchEntity.getIsMergeBatch().equals(1)) {
            List<StockoutBatchEntity> list = stockoutBatchService.list(new LambdaQueryWrapper<StockoutBatchEntity>().eq(StockoutBatchEntity::getMergeBatchId, stockoutBatchEntity.getBatchId()));
            list.forEach(entity -> {
                if (StockoutWaveTaskStatusEnum.WAIT_PICK.name().equals(entity.getStatus()) || StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name().equals(entity.getStatus()) || StockoutWaveTaskStatusEnum.NEW.name().equals(entity.getStatus())) {
                    StockoutBatchEntity stockoutBatchEntity1 = new StockoutBatchEntity();
                    stockoutBatchEntity1.setUpdateBy(loginInfoService.getName());
                    stockoutBatchEntity1.setBatchId(entity.getBatchId());
                    stockoutBatchEntity1.setMergeBatchId(entity.getMergeBatchId());
                    stockoutBatchEntity1.setStatus(StockoutWaveTaskStatusEnum.PICKING.name());
                    stockoutBatchEntityList.add(stockoutBatchEntity1);
                }
            });
        }
        if (CollectionUtils.isEmpty(stockoutBatchEntityList)) {
            return;
        }
        stockoutBatchService.updateBatchById(stockoutBatchEntityList);
        List<Integer> batchIds = stockoutBatchEntityList.stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList());
        stockoutBatchOrderService.update(new UpdateWrapper<StockoutBatchOrderEntity>().lambda()
                .set(StockoutBatchOrderEntity::getStatus, StockoutOrderStatusEnum.PICKING.name())
                .in(StockoutBatchOrderEntity::getBatchId, batchIds));
        List<Integer> collect = stockoutBatchOrderService.list(new QueryWrapper<StockoutBatchOrderEntity>().lambda().select(StockoutBatchOrderEntity::getStockoutOrderId)
                .in(StockoutBatchOrderEntity::getBatchId, batchIds)).stream().map(StockoutBatchOrderEntity::getStockoutOrderId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            List<StockoutOrderEntity> stockoutOrderEntityList = new ArrayList<>(8);
            stockoutOrderService.listByIds(collect).forEach(orderEntity -> {
                //出库单状态不可回退
                if (StockoutOrderStatusEnum.valueOf(orderEntity.getStatus()).getIndex() >= StockoutOrderStatusEnum.PICKING.getIndex()) {
                    return;
                }
                StockoutOrderEntity stockoutOrderEntity = new StockoutOrderEntity();
                stockoutOrderEntity.setStockoutOrderId(orderEntity.getStockoutOrderId());
                stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.PICKING.name());
                stockoutOrderEntityList.add(stockoutOrderEntity);
            });
            if (CollectionUtils.isEmpty(stockoutOrderEntityList))
                return;
            stockoutOrderService.updateBatchById(stockoutOrderEntityList);
            stockoutOrderLogService.addBatchStockoutOrderLog(StockoutOrderLogTypeEnum.PICKING, stockoutOrderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()),
                    String.format("拣货任务【%s】开始拣货，扫描拣货箱号【%s】", taskId, internalBoxCode));
        }
    }

    public List<StockoutPickingTaskConfirmSpaceArea> buildPickingTaskConfirmSpaceAreaList(StockoutPickingTaskEntity taskEntity, List<StockoutPickingTaskItemEntity> pickingTaskItemEntityList, List<StockoutPickingItemRecordEntity> pickingItemRecordEntityList) {
        Map<String, List<StockoutPickingItemRecordEntity>> collect = pickingItemRecordEntityList.stream().collect(Collectors.groupingBy(StockoutPickingItemRecordEntity::getSpaceAreaName));
        return pickingTaskItemEntityList.stream().collect(Collectors.groupingBy(StockoutPickingTaskItemEntity::getSpaceAreaName)).entrySet().stream().map(entry -> {
            StockoutPickingTaskConfirmSpaceArea area = new StockoutPickingTaskConfirmSpaceArea();
            List<StockoutPickingTaskItemEntity> value = entry.getValue();
            area.setSpaceAreaName(entry.getKey());
            area.setExpectedQty(value.stream().mapToInt(StockoutPickingTaskItemEntity::getExpectedQty).sum());
            List<StockoutPickingItemRecordEntity> recordEntities = collect.get(entry.getKey());
            if (!CollectionUtils.isEmpty(recordEntities)) {
                Long count = recordEntities.stream().map(StockoutPickingItemRecordEntity::getInternalBoxCode).distinct().count();
                area.setInternalBoxQty(count.intValue());
            } else {
                area.setInternalBoxQty(0);
            }
            area.setIsLack(value.stream().filter(entity -> entity.getIsLack().equals(1)).findAny().isPresent() ? 1 : 0);
            area.setOperator(value.stream().filter(item -> StringUtils.hasText(item.getOperator())).map(StockoutPickingTaskItemEntity::getOperator).distinct().collect(Collectors.joining(",")));
            area.setPickedQty(value.stream().mapToInt(StockoutPickingTaskItemEntity::getPickedQty).sum());
            if (area.getExpectedQty().equals(area.getPickedQty())) {
                area.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
            } else if (value.stream().allMatch(entity -> entity.getExpectedQty().equals(entity.getPickedQty())
                    || entity.getIsLack().equals(1))) {
                area.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
            } else if (area.getPickedQty().equals(0)) {
                area.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
            } else {
                area.setStatus(StockoutPickingTaskStatusEnum.PICKING.name());
            }
            return area;
        }).collect(Collectors.toList());
    }

    public List<StockoutPickingTaskConfirmSku> buildPickingTaskConfirmSkuList(Integer batchId, List<StockoutPickingTaskItemEntity> pickingTaskItemEntityList, Map<Integer, List<StockoutPickingItemRecordEntity>> recordMap) {
        List<ProductSpecInfoEntity> specInfoEntityList = productSpecInfoService.findAllBySpecIdIn(pickingTaskItemEntityList.stream()
                .map(StockoutPickingTaskItemEntity::getSpecId).collect(Collectors.toList()));
        List<ProductInfoEntity> productInfoEntityList = productInfoService.findByProductIds(pickingTaskItemEntityList.stream()
                .map(StockoutPickingTaskItemEntity::getProductId).collect(Collectors.toList()));
        Map<String, StockoutOrderSkuDescription> skuDescription = stockoutOrderItemService.getBaseMapper().getPickingDescription(batchId).stream().collect(Collectors.toMap(StockoutOrderSkuDescription::getSku, stockoutOrderSkuDescription -> stockoutOrderSkuDescription));
        HashMap<Integer, ProductSpecInfoEntity> specMap = new HashMap<>();
        HashMap<Integer, String> productMap = new HashMap<>();
        StockoutBatchEntity stockoutBatchById = stockoutBatchService.getStockoutBatchById(batchId);
        specInfoEntityList.stream().forEach(productSpecInfoEntity -> specMap.put(productSpecInfoEntity.getSpecId(), productSpecInfoEntity));
        productInfoEntityList.stream().forEach(detail -> {
            if (!StringUtils.hasText(detail.getPackageVacuum())) {
                return;
            }
            productMap.put(detail.getProductId(), detail.getPackageVacuum());
        });
        Map<String, List<String>> skuTagMap = stockoutBatchById.getWorkspace().contains("FBA") ? tagMappingService.getProductTagBySkus(pickingTaskItemEntityList.stream()
                .map(StockoutPickingTaskItemEntity::getSku).collect(Collectors.toList())) : Collections.emptyMap();
        List<StockoutPickingTaskConfirmSku> collect = pickingTaskItemEntityList.stream().map(itemEntity -> getStockoutPickingTaskConfirmSku(recordMap, specMap, productMap, itemEntity, skuDescription, skuTagMap))
                .sorted(Comparator.comparing(StockoutPickingTaskConfirmSku::getSpaceAreaSort).thenComparing(StockoutPickingTaskConfirmSku::getSku)).collect(Collectors.toList());
        return mergeItemList(collect);
    }

    public List<StockoutPickingTaskConfirmSku> mergeItemList(List<StockoutPickingTaskConfirmSku> stockoutPickingTaskConfirmSkuList) {
        Map<String, List<StockoutPickingTaskConfirmSku>> collect = stockoutPickingTaskConfirmSkuList.stream().collect(Collectors.groupingBy(item -> item.getSku() + "_" + item.getPositionCode()));
        return collect.entrySet().stream().map(entry -> {
            if (entry.getValue().size() == 1) {
                return entry.getValue().get(0);
            } else {
                List<StockoutPickingTaskConfirmSku> value = entry.getValue();
                StockoutPickingTaskConfirmSku stockoutPickingTaskConfirmSku = value.get(0);
                List<String> taskItemIds = value.stream().map(item -> item.getTaskItemId()).collect(Collectors.toList());
                stockoutPickingTaskConfirmSku.setTaskItemId(StringUtils.join(taskItemIds, ','));
                stockoutPickingTaskConfirmSku.setExpectedQty(value.stream().mapToInt(StockoutPickingTaskConfirmSku::getExpectedQty).sum());
                stockoutPickingTaskConfirmSku.setPickedQty(value.stream().mapToInt(StockoutPickingTaskConfirmSku::getPickedQty).sum());
                return stockoutPickingTaskConfirmSku;
            }
        }).collect(Collectors.toList());
    }

    public List<StockoutPickingTaskConfirmSpaceAreaSku> mergeItemListArea(List<StockoutPickingTaskConfirmSpaceAreaSku> stockoutPickingTaskConfirmSkuList) {
        Map<String, List<StockoutPickingTaskConfirmSpaceAreaSku>> collect = stockoutPickingTaskConfirmSkuList.stream().collect(Collectors.groupingBy(item -> item.getSku() + "_" + item.getPositionCode()));
        return collect.entrySet().stream().map(entry -> {
            if (entry.getValue().size() == 1) {
                return entry.getValue().get(0);
            } else {
                List<StockoutPickingTaskConfirmSpaceAreaSku> value = entry.getValue();
                StockoutPickingTaskConfirmSpaceAreaSku stockoutPickingTaskConfirmSku = value.get(0);
                List<String> taskItemIds = value.stream().map(item -> item.getTaskItemId()).collect(Collectors.toList());
                stockoutPickingTaskConfirmSku.setTaskItemId(StringUtils.join(taskItemIds, ','));
                stockoutPickingTaskConfirmSku.setExpectedQty(value.stream().mapToInt(StockoutPickingTaskConfirmSpaceAreaSku::getExpectedQty).sum());
                stockoutPickingTaskConfirmSku.setPickedQty(value.stream().mapToInt(StockoutPickingTaskConfirmSpaceAreaSku::getPickedQty).sum());
                return stockoutPickingTaskConfirmSku;
            }
        }).collect(Collectors.toList());
    }


    @NotNull
    private StockoutPickingTaskConfirmSku getStockoutPickingTaskConfirmSku(Map<Integer, List<StockoutPickingItemRecordEntity>> recordMap, Map<Integer, ProductSpecInfoEntity> specMap, Map<Integer, String> productMap,
                                                                           StockoutPickingTaskItemEntity itemEntity, Map<String, StockoutOrderSkuDescription> skuDescription, Map<String, List<String>> skuTagMap) {
        StockoutPickingTaskConfirmSku sku = new StockoutPickingTaskConfirmSku();
        BeanUtilsEx.copyProperties(itemEntity, sku, "taskItemId");
        sku.setPackageVacuum(productMap.get(itemEntity.getProductId()));
        sku.setTaskItemId(itemEntity.getTaskItemId().toString());
        sku.setLackQty(itemEntity.getIsLack().equals(1) ? itemEntity.getExpectedQty() - itemEntity.getPickedQty() : 0);
        List<StockoutPickingItemRecordEntity> recordEntities = recordMap.get(itemEntity.getTaskItemId());
        if (!CollectionUtils.isEmpty(recordEntities)) {
            List<StockoutOrderTaskItemDetail> collect = recordEntities.stream().map(entity -> {
                StockoutOrderTaskItemDetail detail = new StockoutOrderTaskItemDetail();
                detail.setQty(entity.getPickedQty());
                detail.setInternalBoxCode(entity.getInternalBoxCode());
                detail.setSku(entity.getSku());
                return detail;
            }).collect(Collectors.toList());
            sku.setStockoutPickingTaskItemDetailList(collect);
        }
        ProductSpecInfoEntity productSpecInfoEntity = specMap.get(itemEntity.getSpecId());
        if (productSpecInfoEntity != null) {
            sku.setImageUrl(productSpecInfoEntity.getImageUrl());
            sku.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
            sku.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());
        }
        if (itemEntity.getExpectedQty().equals(itemEntity.getPickedQty())) {
            sku.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
        } else {
            if (itemEntity.getIsLack().equals(1)) {
                sku.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
            } else if (sku.getPickedQty().equals(0)) {
                sku.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
            } else {
                sku.setStatus(StockoutPickingTaskStatusEnum.PICKING.name());
            }
        }
        StockoutOrderSkuDescription description = skuDescription.get(itemEntity.getSku());
        sku.setDescription(Objects.nonNull(description) ? description.getDescription() : "");
        sku.setIsFirstOrderByStore(Objects.nonNull(description) ? description.getIsFirstOrderByStore() : 0);
        sku.setIsLeadGeneration(Objects.nonNull(description) ? description.getIsLeadGeneration() : Boolean.FALSE);
        sku.setProductTag(skuTagMap.get(sku.getSku()));
        if (sku.getSpaceAreaId() != null) {
            BdSpaceArea spaceAreaById = spaceAreaService.getSpaceAreaById(sku.getSpaceAreaId());
            if (spaceAreaById != null) {
                sku.setSpaceAreaSort(spaceAreaById.getSort());
            }
        }
        return sku;
    }

    public StockUpdateRequest buildStockUpdateRequest(StockoutPickingTaskItemEntity taskItemEntity) {
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.STOCKOUT_PICKING);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_OUT);
        stockUpdateRequest.setPositionCode(taskItemEntity.getPositionCode());
        stockUpdateRequest.setSku(taskItemEntity.getSku());
        stockUpdateRequest.setQty(taskItemEntity.getPickedQty() - taskItemEntity.getExpectedQty());
        return stockUpdateRequest;
    }
}
